# 🚀 Como Instalar - Raspberry Pi Bootstrap

## 📁 **Arquivos Necessários no Raspberry Pi**

Você precisa de apenas **2 arquivos** no Raspberry Pi:

1. ✅ `bootstrap_first_boot.sh` - Script que baixa e executa do GitHub
2. ✅ `install_bootstrap_service.sh` - Configura o serviço automático

## 🔧 **Passo a Passo da Instalação**

### **1. Preparar o Raspberry Pi (Uma vez apenas)**

```bash
# 1. Copie os 2 arquivos para o Raspberry Pi
scp bootstrap_first_boot.sh install_bootstrap_service.sh suportenv@IP_DO_RASPBERRY:/home/<USER>/

# 2. Acesse o Raspberry Pi
ssh suportenv@IP_DO_RASPBERRY

# 3. Execute a preparação da imagem
sudo ./prepare_bootstrap_image.sh
```

### **2. Criar Imagem Definitiva**

```bash
# 4. Desligue o Raspberry Pi
sudo shutdown -h now

# 5. <PERSON><PERSON> o SD card usando:
# - Raspberry Pi Imager
# - Win32DiskImager  
# - dd command
```

### **3. Usar a Imagem (Para cada novo Raspberry Pi)**

```bash
# 1. Grave a imagem no SD card
# 2. Insira no Raspberry Pi
# 3. Conecte à internet
# 4. Ligue o sistema
# 5. Aguarde 10-15 minutos (instalação automática)
# 6. Acesse monitora.nvirtual.com.br e altere nome do proxy
```

## 🎯 **O que Acontece Automaticamente**

1. **No primeiro boot:**
   - 🌐 Sistema aguarda internet
   - 📥 Baixa script atual do GitHub
   - 🔧 Executa instalação automática
   - ✅ Instala Zabbix + Tactical RMM
   - 🔄 Reinicia sistema

2. **Resultado:**
   - Sistema 100% configurado
   - Monitoramento ativo
   - Pronto para uso

## 📋 **Configurações Aplicadas**

- **Zabbix Hostname:** "MUDAR"
- **Tactical Client ID:** 1
- **Tactical Site ID:** 1
- **Usuário:** suportenv
- **Zabbix Server:** monitora.nvirtual.com.br

## 🔄 **Vantagens da Solução Bootstrap**

- ✅ **Uma imagem para sempre** - nunca mais recriar
- ✅ **Scripts sempre atualizados** - baixa do GitHub
- ✅ **Manutenção fácil** - edite apenas no GitHub
- ✅ **Zero duplicação** - repositório centralizado

## 🛠️ **Comandos Úteis (Após Instalação)**

```bash
# Verificar status
check-bootstrap

# Ver logs
sudo journalctl -u raspberry-bootstrap.service -f

# Forçar nova execução
sudo rm /var/lib/first_boot_completed
sudo systemctl start raspberry-bootstrap.service
```

## 📝 **Para Atualizações Futuras**

1. ✅ Edite scripts no GitHub
2. ✅ Novos deployments usam versão atualizada automaticamente
3. ✅ **Não precisa recriar imagem!**

## 🎉 **Resumo**

- **Arquivos no Raspberry Pi:** Apenas 2
- **Imagens a manter:** Apenas 1 (definitiva)
- **Atualizações:** Apenas no GitHub
- **Manutenção:** Mínima

**Desenvolvido por: Paulo Matheus - NVirtual**
