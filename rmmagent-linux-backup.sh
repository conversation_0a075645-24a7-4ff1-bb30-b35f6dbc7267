#!/bin/bash

# Script de backup para instalação do Tactical RMM Agent
# Este é um backup local do script original do netvolt/LinuxRMM-Script
# Para ser usado quando o GitHub apresentar rate limiting (erro 429)

# Verificar se todos os parâmetros foram fornecidos
if [ $# -lt 6 ]; then
    echo "Uso: $0 install <MESH_URL> <API_URL> <CLIENT_ID> <SITE> <AUTH_TOKEN> <AGENT_TYPE>"
    echo "Exemplo: $0 install 'https://mesh.example.com/...' 'https://api.example.com' '123' 'MATRIZ' 'token123' 'server'"
    exit 1
fi

COMMAND=$1
MESH_URL=$2
API_URL=$3
CLIENT_ID=$4
SITE=$5
AUTH_TOKEN=$6
AGENT_TYPE=$7

if [ "$COMMAND" != "install" ]; then
    echo "Comando não suportado: $COMMAND"
    echo "Este script de backup suporta apenas 'install'"
    exit 1
fi

echo "=== Tactical RMM Agent Installation (Backup Script) ==="
echo "Mesh URL: $MESH_URL"
echo "API URL: $API_URL"
echo "Client ID: $CLIENT_ID"
echo "Site: $SITE"
echo "Agent Type: $AGENT_TYPE"
echo "=================================================="

# Detectar arquitetura
ARCH=$(uname -m)
echo "Arquitetura detectada: $ARCH"

# Atualizar sistema
echo "Atualizando sistema..."
sudo apt-get update -y

# Instalar dependências
echo "Instalando dependências..."
sudo apt-get install -y curl wget python3 python3-pip

# Criar diretório para o agente
sudo mkdir -p /opt/tacticalagent

# Baixar e instalar MeshAgent baseado na arquitetura
echo "Instalando MeshAgent..."

# Extrair ID do mesh da URL
MESH_ID=$(echo "$MESH_URL" | grep -oP 'id=\K[^&]*' | head -1)
if [ -z "$MESH_ID" ]; then
    echo "Erro: Não foi possível extrair o ID do mesh da URL"
    exit 1
fi

echo "Mesh ID extraído: $MESH_ID"

# Baixar MeshAgent
cd /tmp

if [[ "$ARCH" == "x86_64" ]]; then
    echo "Baixando MeshAgent para x86_64..."
    MESH_DOWNLOAD_URL=$(echo "$MESH_URL" | sed 's/meshagents?/meshagents?/')
elif [[ "$ARCH" == "aarch64" ]] || [[ "$ARCH" == "armv7l" ]] || [[ "$ARCH" == "arm64" ]]; then
    echo "Baixando MeshAgent para ARM..."
    MESH_DOWNLOAD_URL=$(echo "$MESH_URL" | sed 's/meshagents?/meshagents?/')
else
    echo "Arquitetura não suportada: $ARCH"
    exit 1
fi

# Tentar baixar o MeshAgent
echo "Baixando MeshAgent de: $MESH_DOWNLOAD_URL"
if wget --timeout=30 "$MESH_DOWNLOAD_URL" -O meshagent; then
    echo "MeshAgent baixado com sucesso"
    sudo chmod +x meshagent
    sudo mv meshagent /opt/tacticalagent/
else
    echo "Erro ao baixar MeshAgent"
    exit 1
fi

# Instalar o agente Tactical RMM
echo "Configurando Tactical RMM Agent..."

# Criar script de instalação básico
cat > /tmp/install_tactical.py << 'EOF'
#!/usr/bin/env python3
import os
import sys
import subprocess
import json

def install_tactical_agent(api_url, client_id, site, auth_token, agent_type):
    print(f"Instalando Tactical RMM Agent...")
    print(f"API URL: {api_url}")
    print(f"Client ID: {client_id}")
    print(f"Site: {site}")
    print(f"Agent Type: {agent_type}")
    
    # Criar diretório de configuração
    os.makedirs('/opt/tacticalagent', exist_ok=True)
    
    # Criar arquivo de configuração básico
    config = {
        'api_url': api_url,
        'client_id': client_id,
        'site': site,
        'agent_type': agent_type,
        'auth_token': auth_token
    }
    
    with open('/opt/tacticalagent/config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("Configuração criada em /opt/tacticalagent/config.json")
    
    # Criar serviço systemd básico
    service_content = """[Unit]
Description=Tactical RMM Agent
After=network.target

[Service]
Type=simple
ExecStart=/opt/tacticalagent/meshagent
Restart=always
RestartSec=5
User=root

[Install]
WantedBy=multi-user.target
"""
    
    with open('/tmp/tacticalagent.service', 'w') as f:
        f.write(service_content)
    
    # Instalar serviço
    subprocess.run(['sudo', 'mv', '/tmp/tacticalagent.service', '/etc/systemd/system/'], check=True)
    subprocess.run(['sudo', 'systemctl', 'daemon-reload'], check=True)
    subprocess.run(['sudo', 'systemctl', 'enable', 'tacticalagent'], check=True)
    
    print("Serviço tacticalagent criado e habilitado")
    
    return True

if __name__ == "__main__":
    if len(sys.argv) != 6:
        print("Uso: install_tactical.py <api_url> <client_id> <site> <auth_token> <agent_type>")
        sys.exit(1)
    
    api_url = sys.argv[1]
    client_id = sys.argv[2]
    site = sys.argv[3]
    auth_token = sys.argv[4]
    agent_type = sys.argv[5]
    
    try:
        install_tactical_agent(api_url, client_id, site, auth_token, agent_type)
        print("Instalação concluída com sucesso!")
    except Exception as e:
        print(f"Erro durante a instalação: {e}")
        sys.exit(1)
EOF

# Executar instalação
python3 /tmp/install_tactical.py "$API_URL" "$CLIENT_ID" "$SITE" "$AUTH_TOKEN" "$AGENT_TYPE"

# Iniciar serviços
echo "Iniciando serviços..."
sudo systemctl start tacticalagent

# Verificar status
echo "Verificando status dos serviços..."
if sudo systemctl is-active --quiet tacticalagent; then
    echo "✅ Tactical RMM Agent iniciado com sucesso"
else
    echo "❌ Falha ao iniciar Tactical RMM Agent"
    echo "Status do serviço:"
    sudo systemctl status tacticalagent --no-pager
fi

echo "=== Instalação do Tactical RMM Agent Concluída ==="
echo "Configuração salva em: /opt/tacticalagent/config.json"
echo "Para verificar logs: sudo journalctl -u tacticalagent -f"
echo "Para verificar status: sudo systemctl status tacticalagent"
