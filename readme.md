# Instalação Automatizada - Zabbix + Tactical RMM

Este repositório contém scripts para instalação automatizada do Zabbix Proxy, Zabbix Agent e Tactical RMM em Ubuntu Server 24.04, incluindo configuração automática de IP fixo.

## Arquivos Incluídos

- `install_zabbix_tactical_rmm.sh` - Script principal de instalação
- `check_installation.sh` - Script de verificação e troubleshooting
- `INSTALACAO_ZABBIX_TACTICAL.md` - Este arquivo de documentação

## Pré-requisitos

- Ubuntu Server 24.04 LTS (instalação limpa)
- Acesso à internet
- Usuário com privilégios sudo (não executar como root)
- Conexão de rede ativa (DHCP inicialmente)

## Funcionalidades

### Configuração Automática de Rede
- Detecta automaticamente o IP atual obtido via DHCP
- Identifica a faixa de rede (ex: 192.168.15.x)
- **Verifica se o IP .222 já está em uso** (ping test)
- Se disponível: configura IP fixo terminando em .222 (ex: **************)
- Se ocupado: mantém DHCP ou permite escolher outro IP
- Preserva gateway e DNS existentes
- Aplica configuração via netplan

### Instalação do Zabbix
- **Zabbix Proxy 7.0** com SQLite3
- **Zabbix Agent** para monitoramento local
- Configuração automática para servidor `monitora.nvirtual.com.br`
- Otimizações de performance (pollers, pingers, etc.)
- Habilitação de comandos remotos

### Instalação do Tactical RMM
- **Tactical RMM Agent** via script do netvolt/LinuxRMM-Script
- **MeshAgent** para conectividade remota
- Configuração automática com servidores centralmesh.nvirtual.com.br
- Requer ID do Cliente e Filial durante a instalação

### Configuração de Segurança
- Firewall UFW com regras básicas
- Portas liberadas: 22, 80, 443, 10050, 10051

### 🔄 **Upgrade Automático do Zabbix (NOVO v1.17.0)**
- 🔍 **Detecção automática** de versões instaladas (6.4, 6.0, 5.x, etc.)
- 📊 **Comparação inteligente** de versões (< 7.0 = upgrade necessário)
- 🗑️ **Remoção completa** de instalações incompatíveis
- 🧹 **Limpeza de repositórios** e configurações antigas
- ⬆️ **Instalação limpa** do Zabbix 7.0
- ⚡ **Processo totalmente automático** sem intervenção manual
- 🛡️ **Backup de configurações** antes da remoção

## Como Usar

### 1. Preparação

#### Opção A: Instalação Rápida (Recomendada)
```bash
# Em um Ubuntu Server 24.04 limpo:
wget -O quick_install.sh https://raw.githubusercontent.com/nubium-cloud/Zabbix-Proxy-Instalation/main/quick_install.sh
chmod +x quick_install.sh
./quick_install.sh
```

#### Opção B: Instalação Rápida Segura (Token Interativo)
```bash
# Versão que solicita token durante execução:
wget -O quick_install_secure.sh https://raw.githubusercontent.com/nubium-cloud/Zabbix-Proxy-Instalation/main/quick_install_secure.sh
chmod +x quick_install_secure.sh
./quick_install_secure.sh
```

#### Opção C: Download Manual
```bash
# Fazer download dos scripts (requer token GitHub para repositório privado)
TOKEN="seu_token_github_aqui"
wget --header="Authorization: token $TOKEN" https://raw.githubusercontent.com/nubium-cloud/Zabbix-Proxy-Instalation/main/install_zabbix_tactical_rmm.sh
wget --header="Authorization: token $TOKEN" https://raw.githubusercontent.com/nubium-cloud/Zabbix-Proxy-Instalation/main/check_installation.sh

# Dar permissão de execução
chmod +x install_zabbix_tactical_rmm.sh
chmod +x check_installation.sh
```

### 2. Execução da Instalação
```bash
# Executar o script principal
./install_zabbix_tactical_rmm.sh
```

### 3. Informações Solicitadas
Durante a execução, o script solicitará:

1. **Nome do Zabbix Proxy**: Nome único para identificar este proxy no servidor Zabbix
   - Exemplo: `cliente-zbxproxy`, `empresa-proxy01`

2. **ID do Cliente (Tactical RMM)**: Identificador numérico do cliente
   - Exemplo: `123`, `456`

3. **Filial do Cliente (Tactical RMM)**: Nome ou código da filial
   - Exemplo: `MATRIZ`, `FILIAL01`, `SP`

4. **Configuração de IP**: O script detectará automaticamente a rede e verificará se o IP .222 está disponível
   - Se disponível: oferece configurar IP fixo .222
   - Se ocupado: mantém DHCP ou permite escolher outro IP
   - Opções: IP fixo personalizado, manter DHCP, ou usar .222 se disponível

### 4. Verificação da Instalação
```bash
# Executar script de verificação
./check_installation.sh
```

## Configurações Aplicadas

### Zabbix Proxy (`/etc/zabbix/zabbix_proxy.conf`)
```ini
Server=monitora.nvirtual.com.br
Hostname=[nome-informado]
StartPollers=20
StartPingers=10
StartDiscoverers=10
StartPollersUnreachable=10
EnableRemoteCommands=1
DBName=/tmp/zabbix
```

### Zabbix Agent (`/etc/zabbix/zabbix_agentd.conf`)
```ini
Server=127.0.0.1
ServerActive=127.0.0.1
Hostname=[nome-informado]
EnableRemoteCommands=1
```

### Tactical RMM Agent
```bash
# Instalação via script netvolt/LinuxRMM-Script
./rmmagent-linux.sh install \
  'https://mesh.centralmesh.nvirtual.com.br/meshagents?id=...' \
  'https://api.centralmesh.nvirtual.com.br' \
  '[ID_CLIENT]' \
  '[FILIAL_CLIENT]' \
  '7514f475df4c5f1303120fd65b18fb16b8f6baf06f73d1c2cfd4ebf83862eb82' \
  'server'
```

### Configuração de Rede (`/etc/netplan/01-netcfg.yaml`)
```yaml
network:
  version: 2
  renderer: networkd
  ethernets:
    [interface]:
      dhcp4: false
      addresses:
        - [ip-fixo]/24
      gateway4: [gateway-detectado]
      nameservers:
        addresses: [dns-detectados]
```

## 🔧 **Troubleshooting e Diagnósticos**

### 🔍 **Verificação Rápida do Sistema**

```bash
# Script de verificação automática (recomendado)
./check_installation.sh

# Verificação manual dos serviços
sudo systemctl status zabbix-proxy zabbix-agent tacticalagent

# Verificar se todos os serviços estão ativos
sudo systemctl is-active zabbix-proxy zabbix-agent tacticalagent
```

### 📊 **Verificar Status Detalhado dos Serviços**

```bash
# Status completo do Zabbix Proxy
sudo systemctl status zabbix-proxy --no-pager -l

# Status completo do Zabbix Agent
sudo systemctl status zabbix-agent --no-pager -l

# Status completo do Tactical RMM
sudo systemctl status tacticalagent --no-pager -l

# Verificar se os serviços estão habilitados para inicialização automática
sudo systemctl is-enabled zabbix-proxy zabbix-agent tacticalagent
```

### 📝 **Verificar Logs em Tempo Real**

```bash
# Logs do Zabbix Proxy (tempo real)
sudo journalctl -u zabbix-proxy -f

# Logs do Zabbix Agent (tempo real)
sudo journalctl -u zabbix-agent -f

# Logs do Tactical RMM (tempo real)
sudo journalctl -u tacticalagent -f

# Todos os logs relacionados ao Zabbix
sudo journalctl -u zabbix-* -f

# Logs das últimas 50 linhas
sudo journalctl -u zabbix-proxy -n 50
sudo journalctl -u zabbix-agent -n 50
```

### 📁 **Arquivos de Log Específicos**

```bash
# Logs do Zabbix Proxy
sudo tail -f /var/log/zabbix/zabbix_proxy.log

# Logs do Zabbix Agent
sudo tail -f /var/log/zabbix/zabbix_agentd.log

# Verificar se os arquivos de log existem
ls -la /var/log/zabbix/

# Verificar permissões dos arquivos de log
sudo ls -la /var/log/zabbix/
```

### 🌐 **Testar Conectividade de Rede**

```bash
# Testar conexão com servidor Zabbix
ping -c 5 monitora.nvirtual.com.br
telnet monitora.nvirtual.com.br 10051

# Testar resolução DNS
nslookup monitora.nvirtual.com.br
dig monitora.nvirtual.com.br

# Testar agent local
zabbix_get -s localhost -k system.hostname
zabbix_get -s localhost -k agent.ping

# Testar conectividade Tactical RMM
ping -c 5 api.centralmesh.nvirtual.com.br
ping -c 5 mesh.centralmesh.nvirtual.com.br

# Testar HTTPS dos servidores Tactical RMM
curl -I https://api.centralmesh.nvirtual.com.br
curl -I https://mesh.centralmesh.nvirtual.com.br

# Verificar portas abertas localmente
sudo netstat -tlnp | grep -E "(10050|10051)"
sudo ss -tlnp | grep -E "(10050|10051)"
```

### 🔄 **Reiniciar Serviços**

```bash
# Reiniciar todos os serviços
sudo systemctl restart zabbix-proxy zabbix-agent tacticalagent

# Reiniciar individualmente
sudo systemctl restart zabbix-proxy
sudo systemctl restart zabbix-agent
sudo systemctl restart tacticalagent

# Parar e iniciar (se restart não funcionar)
sudo systemctl stop zabbix-proxy
sudo systemctl start zabbix-proxy

# Recarregar configurações sem reiniciar
sudo systemctl reload zabbix-proxy
sudo systemctl reload zabbix-agent
```

### Problemas Comuns

#### 1. Conflito de IP (.222 já em uso)
```bash
# Verificar se o IP .222 está realmente em uso
ping -c 5 192.168.X.222  # Substitua X pela sua rede

# Se responder, há conflito. Opções:
# 1. Manter DHCP (recomendado)
# 2. Usar outro IP fixo (ex: .223, .224)
# 3. Identificar e reconfigurar o equipamento que usa .222

# Para voltar ao DHCP se configurou IP fixo:
sudo cp /etc/netplan/01-netcfg.yaml.backup.* /etc/netplan/01-netcfg.yaml
sudo netplan apply
```

#### 2. Falha na Conectividade Após Configurar IP Fixo
```bash
# Verificar configuração
cat /etc/netplan/01-netcfg.yaml

# Reaplicar configuração
sudo netplan apply

# Restaurar backup se necessário
sudo cp /etc/netplan/01-netcfg.yaml.backup.* /etc/netplan/01-netcfg.yaml
sudo netplan apply
```

#### 3. Zabbix Proxy Não Conecta ao Servidor
```bash
# Verificar conectividade
ping monitora.nvirtual.com.br
telnet monitora.nvirtual.com.br 10051

# Verificar configuração
grep -E "^(Server|Hostname)" /etc/zabbix/zabbix_proxy.conf
```

#### 4. Problemas de Firewall
```bash
# Verificar status
sudo ufw status

# Reconfigurar se necessário
sudo ufw allow 10050/tcp
sudo ufw allow 10051/tcp
sudo ufw reload
```

#### 5. Tactical RMM Agent Não Conecta
```bash
# Verificar se o agente está instalado
ls -la /opt/tacticalagent/

# Verificar se o MeshAgent está rodando
ps aux | grep meshagent

# Verificar logs do agente
sudo journalctl -u tacticalagent -n 50

# Reinstalar se necessário
cd /tmp
wget https://raw.githubusercontent.com/netvolt/LinuxRMM-Script/main/rmmagent-linux.sh
chmod +x rmmagent-linux.sh
./rmmagent-linux.sh install [parâmetros]
```

#### 6. Problemas de Conectividade com Servidores Tactical RMM
```bash
# Testar DNS
nslookup api.centralmesh.nvirtual.com.br
nslookup mesh.centralmesh.nvirtual.com.br

# Testar conectividade HTTPS
curl -I https://api.centralmesh.nvirtual.com.br
curl -I https://mesh.centralmesh.nvirtual.com.br

# Verificar se não há proxy/firewall bloqueando
telnet api.centralmesh.nvirtual.com.br 443
telnet mesh.centralmesh.nvirtual.com.br 443
```

## 🔌 **Portas Utilizadas**

| Serviço | Porta | Protocolo | Direção | Descrição |
|---------|-------|-----------|---------|-----------|
| **SSH** | 22 | TCP | Entrada | Acesso remoto administrativo |
| **HTTP** | 80 | TCP | Entrada | Tactical RMM (redirecionamento) |
| **HTTPS** | 443 | TCP | Entrada/Saída | Tactical RMM (comunicação segura) |
| **Zabbix Agent** | 10050 | TCP | Entrada | Monitoramento local |
| **Zabbix Proxy** | 10051 | TCP | Saída | Comunicação com servidor Zabbix |

### 🔒 **Configuração de Firewall Automática**
- ✅ **UFW** instalado e configurado automaticamente
- ✅ **Fallback para iptables** se UFW não disponível
- ✅ **Regras básicas** aplicadas automaticamente
- ✅ **Portas essenciais** liberadas

## 💾 **Arquivos de Backup Automáticos**

O script cria backups automáticos com timestamp dos arquivos de configuração:

```bash
# Configuração de rede
/etc/netplan/01-netcfg.yaml.backup.[YYYYMMDD_HHMMSS]

# Configurações do Zabbix
/etc/zabbix/zabbix_proxy.conf.backup.[YYYYMMDD_HHMMSS]
/etc/zabbix/zabbix_agentd.conf.backup.[YYYYMMDD_HHMMSS]

# Verificar backups criados
ls -la /etc/netplan/*.backup.*
ls -la /etc/zabbix/*.backup.*
```

## 📊 **Repositórios Utilizados por Versão/Arquitetura**

### **Ubuntu 22.04 (Jammy)**
```bash
# x86_64
deb [signed-by=/usr/share/keyrings/zabbix-official-repo.gpg] https://repo.zabbix.com/zabbix/7.0/ubuntu jammy main
```

### **Ubuntu 24.04 (Noble)**
```bash
# x86_64
deb [signed-by=/usr/share/keyrings/zabbix-official-repo.gpg] https://repo.zabbix.com/zabbix/7.0/ubuntu noble main
```

### **ARM/Raspberry Pi (Bullseye)**
```bash
# ARM64/ARMv7l
deb [signed-by=/usr/share/keyrings/zabbix-official-repo.gpg] https://repo.zabbix.com/zabbix/7.0/raspbian bullseye main
```

## 🆘 **Suporte e Ajuda**

### **Diagnóstico Automático**
```bash
# Execute sempre primeiro para diagnóstico completo
./check_installation.sh
```

### **Para Problemas ou Dúvidas:**
1. 🔍 **Execute o script de verificação** `check_installation.sh`
2. 📝 **Verifique os logs** dos serviços conforme documentado
3. 🌐 **Teste a conectividade** de rede
4. 📚 **Consulte a documentação oficial**:
   - [Zabbix 7.0 Documentation](https://www.zabbix.com/documentation/7.0/)
   - [Tactical RMM Documentation](https://docs.tacticalrmm.com/)
   - [Ubuntu Netplan Documentation](https://netplan.io/)

### **Informações do Sistema**
```bash
# Verificar versão do script
grep "Software Versão" install_zabbix_tactical_rmm.sh

# Verificar compatibilidade do sistema
lsb_release -a && uname -m
```

## 📋 **Changelog**

### **v1.16.0** (Atual)
- ✅ **NOVO**: Suporte Ubuntu 20.04 LTS (Focal Fossa)
- ✅ **NOVO**: Proteção SSH inteligente durante configuração de rede
- ✅ **NOVO**: Detecção automática de conexões SSH
- ✅ **NOVO**: Confirmação de segurança antes de alterar IP via SSH
- ✅ **NOVO**: Aplicação segura de netplan em background para SSH
- ✅ **NOVO**: Instruções específicas para reconexão SSH
- ✅ **MELHORADO**: Prevenção de perda de conexão durante configuração de IP

### **v1.15.0**
- ✅ **Suporte completo Ubuntu 22.04 e 24.04**
- ✅ **Suporte completo arquiteturas x86_64 e ARM**
- ✅ **Detecção automática** de versão e arquitetura
- ✅ **Repositórios específicos** por OS/arquitetura
- ✅ **URLs Tactical RMM otimizadas** por arquitetura
- ✅ **Instalação automática do UFW** com fallback iptables
- ✅ **Melhorias no tratamento de erros**
- ✅ **Logs mais detalhados** e informativos
- ✅ **ASCII art corrigido** na apresentação

### **v1.0**
- ✅ Instalação automatizada do Zabbix 7.0
- ✅ Configuração automática de IP fixo
- ✅ Instalação do Tactical RMM
- ✅ Scripts de verificação e troubleshooting

---

## 👨‍💻 **Desenvolvido por**
**Paulo Matheus** - NVirtual
📧 Suporte técnico disponível

---

**🚀 Script de Instalação Automatizada v1.15.0**
**✅ Compatível com Ubuntu 22.04/24.04 (x86_64/ARM)**
**🔧 Instalação 100% automatizada com detecção inteligente**
