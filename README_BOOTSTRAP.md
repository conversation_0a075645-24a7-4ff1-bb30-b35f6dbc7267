# 🔄 Raspberry Pi Bootstrap - Sempre Atualizado

## Descrição

Solução **bootstrap** para Raspberry Pi que baixa e executa automaticamente a versão mais atual dos scripts de configuração diretamente do GitHub. **Nunca mais precise recriar imagens** - sempre use a versão mais recente!

## 🎯 **Vantagens da Solução Bootstrap**

- ✅ **Scripts sempre atualizados** - baixa do GitHub no primeiro boot
- ✅ **Uma imagem para sempre** - não precisa recriar para updates
- ✅ **Manutenção centralizada** - edite apenas no GitHub
- ✅ **Configuração sempre atual** - usa última versão disponível
- ✅ **Token de acesso configurado** - acesso automático ao repositório privado

## 📁 **Arquivos da Solução Bootstrap**

| Arquivo | Descrição |
|---------|-----------|
| `bootstrap_first_boot.sh` | Script principal que baixa e executa do GitHub |
| `install_bootstrap_service.sh` | Configura serviço systemd bootstrap |
| `prepare_bootstrap_image.sh` | Prepara imagem definitiva do Raspberry Pi |
| `README_BOOTSTRAP.md` | Esta documentação |

## 🚀 **Como Funciona**

### 1. **Preparação da Imagem (Uma vez apenas):**
```bash
sudo ./prepare_bootstrap_image.sh
```

### 2. **No Primeiro Boot (Automático):**
1. 🌐 Sistema aguarda conectividade
2. 📥 Bootstrap baixa script atual do GitHub
3. 🔧 Executa configuração automática
4. ✅ Instala Zabbix + Tactical RMM
5. 🔄 Reinicia sistema configurado

### 3. **Resultado:**
- Sistema 100% configurado
- Sempre com versão mais recente
- Sem necessidade de recriar imagem

## 🔧 **Configuração**

### **Repositório GitHub:**
- **Repositório:** `nubium-cloud/Raspberry-First-Boot-Configuration`
- **Script principal:** `setup_first_boot.sh`
- **Token:** Configurado automaticamente

### **Configurações Padrão:**
- **Usuário:** `suportenv` (NVirtual padrão)
- **Zabbix Hostname:** "MUDAR"
- **Tactical Client ID:** 1
- **Tactical Site ID:** 1

## 📋 **Fluxo Completo**

```mermaid
graph TD
    A[Raspberry Pi Liga] --> B[Serviço Bootstrap Inicia]
    B --> C[Aguarda Conectividade]
    C --> D[Baixa Script do GitHub]
    D --> E[Executa Configuração]
    E --> F[Instala Zabbix + Tactical]
    F --> G[Sistema Configurado]
    G --> H[Reinicia Automaticamente]
```

## 🛠️ **Comandos Úteis**

```bash
# Verificar status do bootstrap
check-bootstrap

# Ver logs em tempo real
sudo journalctl -u raspberry-bootstrap.service -f

# Executar bootstrap manualmente
sudo systemctl start raspberry-bootstrap.service

# Forçar nova execução
sudo rm /var/lib/first_boot_completed
sudo systemctl start raspberry-bootstrap.service

# Ver informações do sistema
cat /home/<USER>/SISTEMA_INFO.txt
```

## 📊 **Comparação: Bootstrap vs Tradicional**

| Aspecto | Método Tradicional | **Bootstrap** |
|---------|-------------------|---------------|
| **Atualizações** | Recriar imagem completa | ✅ Editar apenas no GitHub |
| **Manutenção** | Complexa e demorada | ✅ Simples e rápida |
| **Versão** | Pode ficar desatualizada | ✅ Sempre atual |
| **Deploy** | Múltiplas imagens | ✅ Uma imagem definitiva |
| **Tempo** | Horas para recriar | ✅ Minutos para atualizar |

## 🔄 **Processo de Atualização**

### **Método Tradicional (Antigo):**
1. Editar scripts localmente
2. Recriar imagem completa
3. Testar nova imagem
4. Distribuir nova imagem
5. Regravar todos os SD cards

### **Método Bootstrap (Novo):**
1. ✅ Editar scripts no GitHub
2. ✅ Novos deployments usam versão atualizada automaticamente
3. ✅ **FIM!** 🎉

## 📝 **Exemplo de Uso Prático**

### **Cenário:** Você precisa alterar o hostname padrão do Zabbix

**Método Tradicional:**
- ❌ Editar script local
- ❌ Recriar imagem (30+ minutos)
- ❌ Testar nova imagem
- ❌ Distribuir para equipe
- ❌ Regravar SD cards existentes

**Método Bootstrap:**
- ✅ Editar `setup_first_boot.sh` no GitHub (2 minutos)
- ✅ Próximos deployments usam nova configuração automaticamente
- ✅ **Pronto!**

## 🔐 **Segurança**

- Token GitHub com acesso limitado ao repositório
- Scripts baixados apenas de fonte confiável
- Verificação de integridade dos arquivos
- Execução apenas no primeiro boot

## 🧪 **Teste da Solução**

```bash
# 1. Preparar imagem bootstrap
sudo ./prepare_bootstrap_image.sh

# 2. Desligar e clonar SD card
sudo shutdown -h now

# 3. Usar imagem em novo Raspberry Pi
# 4. Conectar à internet e ligar
# 5. Aguardar configuração automática
# 6. Verificar resultado
check-bootstrap
```

## 📄 **Estrutura de Arquivos Criados**

```
/usr/local/bin/
├── bootstrap_first_boot.sh          # Script bootstrap principal
└── check_bootstrap_status.sh        # Script de verificação

/etc/systemd/system/
└── raspberry-bootstrap.service      # Serviço systemd

/var/lib/
└── first_boot_completed             # Flag de execução

/home/<USER>/
├── SISTEMA_INFO.txt                 # Informações pós-instalação
└── IMAGEM_BOOTSTRAP_INFO.txt        # Informações da imagem
```

## 🔧 **Personalização**

### **Alterar Repositório GitHub:**
Edite em `bootstrap_first_boot.sh`:
```bash
GITHUB_REPO="seu-usuario/seu-repositorio"
SCRIPT_NAME="seu_script.sh"
```

### **Alterar Token:**
```bash
GITHUB_TOKEN="seu_novo_token"
```

## 🆘 **Troubleshooting**

### **Bootstrap não executa:**
```bash
sudo systemctl status raspberry-bootstrap.service
sudo journalctl -u raspberry-bootstrap.service
```

### **Erro de conectividade:**
```bash
ping github.com
curl -I https://github.com
```

### **Token inválido:**
```bash
# Verificar acesso ao repositório
curl -H "Authorization: token SEU_TOKEN" \
     https://api.github.com/repos/nubium-cloud/Raspberry-First-Boot-Configuration
```

## 🎉 **Resultado Final**

Com a solução bootstrap você terá:

- 🎯 **Uma imagem definitiva** que nunca precisa ser recriada
- 🔄 **Scripts sempre atualizados** automaticamente
- ⚡ **Deployments rápidos** e consistentes
- 🛠️ **Manutenção simplificada** no GitHub
- 📈 **Escalabilidade** para múltiplos dispositivos

## 👨‍💻 **Desenvolvido por**

**Paulo Matheus - NVirtual**

---

**🚀 Transforme seu processo de deployment com a solução bootstrap!**
