#!/usr/bin/env python3

"""
Network Scanner - Scan de rede ***********/16
Identifica dispositivos e sistemas operacionais na rede
Autor: Paulo Matheus
Data: 2025-01-08
"""

import subprocess
import threading
import ipaddress
import socket
import sys
import time
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse

# Tentar importar openpyxl para Excel
try:
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from openpyxl.utils import get_column_letter
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

class NetworkScanner:
    def __init__(self, network="***********/16", max_workers=100, timeout=2):
        self.network = ipaddress.IPv4Network(network)
        self.max_workers = max_workers
        self.timeout = timeout
        self.results = []
        self.lock = threading.Lock()
        
        # Portas comuns para identificação de serviços
        self.common_ports = {
            22: "SSH",
            23: "Telnet",
            25: "SMTP",
            53: "DNS",
            80: "HTTP",
            110: "POP3",
            135: "RPC",
            139: "NetBIOS",
            143: "IMAP",
            443: "HTTPS",
            445: "SMB",
            993: "IMAPS",
            995: "POP3S",
            1900: "UPnP",
            3389: "RDP",
            5000: "UPnP",
            5353: "mDNS",
            5900: "VNC",
            8080: "HTTP-Alt",
            8443: "HTTPS-Alt",
            9100: "Printer",
            10000: "Webmin",
            62078: "iPhone-Sync"
        }

        # Portas específicas para dispositivos
        self.device_ports = {
            # Roteadores e Network Devices
            23: ["Router", "Switch", "Firewall"],
            80: ["Router", "Access Point", "Firewall", "Server"],
            443: ["Router", "Access Point", "Firewall", "Server"],
            161: ["Router", "Switch", "Firewall", "Printer"],
            162: ["Router", "Switch", "Firewall"],
            8080: ["Router", "Access Point"],
            8443: ["Router", "Access Point"],

            # Dispositivos móveis
            62078: ["iPhone", "iPad"],
            5353: ["Apple Device", "Android"],
            1900: ["Android", "Smart TV"],

            # Servidores e serviços
            22: ["Linux Server", "Unix Server"],
            3389: ["Windows Server"],
            5900: ["VNC Server"],
            9100: ["Printer"],
            10000: ["Linux Server"]
        }
        
        # Assinaturas de OS baseadas em TTL e outras características
        self.os_signatures = {
            64: "Linux/Unix",
            128: "Windows",
            255: "Cisco/Network Device",
            32: "Windows 95/98",
            60: "macOS/iOS"
        }

        # Padrões de hostname para identificação específica
        self.hostname_patterns = {
            # Dispositivos móveis
            'iphone': 'iPhone',
            'ipad': 'iPad',
            'android': 'Android Device',
            'samsung': 'Samsung Device',
            'xiaomi': 'Xiaomi Device',
            'huawei': 'Huawei Device',
            'oneplus': 'OnePlus Device',

            # Roteadores por marca
            'tp-link': 'TP-Link Router',
            'tplink': 'TP-Link Router',
            'dlink': 'D-Link Router',
            'd-link': 'D-Link Router',
            'linksys': 'Linksys Router',
            'netgear': 'Netgear Router',
            'asus': 'ASUS Router',
            'cisco': 'Cisco Device',
            'mikrotik': 'MikroTik Router',
            'ubiquiti': 'Ubiquiti Device',
            'unifi': 'UniFi Access Point',

            # Firewalls
            'pfsense': 'pfSense Firewall',
            'opnsense': 'OPNsense Firewall',
            'sophos': 'Sophos Firewall',
            'fortinet': 'FortiGate Firewall',
            'checkpoint': 'Check Point Firewall',

            # Impressoras
            'hp': 'HP Printer',
            'canon': 'Canon Printer',
            'epson': 'Epson Printer',
            'brother': 'Brother Printer',
            'xerox': 'Xerox Printer',
            'printer': 'Network Printer',

            # Servidores e sistemas
            'ubuntu': 'Ubuntu Server',
            'debian': 'Debian Server',
            'centos': 'CentOS Server',
            'redhat': 'Red Hat Server',
            'windows': 'Windows Machine',
            'server': 'Server',
            'nas': 'NAS Device',
            'synology': 'Synology NAS',
            'qnap': 'QNAP NAS',

            # Smart devices
            'smart-tv': 'Smart TV',
            'chromecast': 'Google Chromecast',
            'roku': 'Roku Device',
            'appletv': 'Apple TV',
            'firetv': 'Amazon Fire TV'
        }

        # Base de dados de modelos conhecidos (quando hostname não é específico)
        self.known_models = {
            # TP-Link Routers
            'archer-c5': 'TP-Link Archer C5 Router',
            'archer-c7': 'TP-Link Archer C7 Router',
            'archer-c9': 'TP-Link Archer C9 Router',
            'archer-c20': 'TP-Link Archer C20 Router',
            'archer-c50': 'TP-Link Archer C50 Router',
            'archer-c60': 'TP-Link Archer C60 Router',
            'archer-c80': 'TP-Link Archer C80 Router',
            'archer-ax10': 'TP-Link Archer AX10 WiFi 6 Router',
            'archer-ax20': 'TP-Link Archer AX20 WiFi 6 Router',
            'archer-ax50': 'TP-Link Archer AX50 WiFi 6 Router',
            'tl-wr841n': 'TP-Link TL-WR841N Router',
            'tl-wr940n': 'TP-Link TL-WR940N Router',
            'tl-wr1043nd': 'TP-Link TL-WR1043ND Router',

            # D-Link Routers
            'dir-615': 'D-Link DIR-615 Router',
            'dir-816': 'D-Link DIR-816 Router',
            'dir-842': 'D-Link DIR-842 Router',
            'dir-878': 'D-Link DIR-878 Router',
            'dir-882': 'D-Link DIR-882 Router',
            'dwr-921': 'D-Link DWR-921 4G Router',

            # Netgear Routers
            'r6120': 'Netgear R6120 Router',
            'r6220': 'Netgear R6220 Router',
            'r6350': 'Netgear R6350 Router',
            'r6700': 'Netgear R6700 Router',
            'r7000': 'Netgear R7000 Nighthawk Router',
            'r8000': 'Netgear R8000 Nighthawk X6 Router',
            'ax12': 'Netgear AX12 WiFi 6 Router',

            # ASUS Routers
            'rt-ac51u': 'ASUS RT-AC51U Router',
            'rt-ac66u': 'ASUS RT-AC66U Router',
            'rt-ac68u': 'ASUS RT-AC68U Router',
            'rt-ac86u': 'ASUS RT-AC86U Router',
            'rt-ax55': 'ASUS RT-AX55 WiFi 6 Router',
            'rt-ax68u': 'ASUS RT-AX68U WiFi 6 Router',

            # Linksys Routers
            'ea6100': 'Linksys EA6100 Router',
            'ea6350': 'Linksys EA6350 Router',
            'ea7300': 'Linksys EA7300 Router',
            'ea8100': 'Linksys EA8100 Router',
            'mr8300': 'Linksys MR8300 Mesh Router',

            # Cisco Devices
            'rv130': 'Cisco RV130 VPN Router',
            'rv160': 'Cisco RV160 VPN Router',
            'rv260': 'Cisco RV260 VPN Router',
            'sg110': 'Cisco SG110 Switch',
            'sg220': 'Cisco SG220 Switch',

            # MikroTik Devices
            'rb750gr3': 'MikroTik hEX (RB750Gr3) Router',
            'rb951ui': 'MikroTik RB951Ui-2HnD Router',
            'rb2011': 'MikroTik RB2011 Router',
            'ccr1009': 'MikroTik CCR1009 Router',

            # Ubiquiti Devices
            'uap-ac-lite': 'Ubiquiti UniFi AP AC Lite',
            'uap-ac-pro': 'Ubiquiti UniFi AP AC Pro',
            'uap-ac-hd': 'Ubiquiti UniFi AP AC HD',
            'uap-wifi6-lite': 'Ubiquiti UniFi 6 Lite',
            'udm-pro': 'Ubiquiti Dream Machine Pro',
            'usg-3p': 'Ubiquiti Security Gateway 3P',

            # Impressoras comuns
            'deskjet-2130': 'HP DeskJet 2130 Printer',
            'deskjet-3630': 'HP DeskJet 3630 Printer',
            'laserjet-p1102': 'HP LaserJet P1102 Printer',
            'laserjet-m404': 'HP LaserJet M404 Printer',
            'pixma-g3110': 'Canon PIXMA G3110 Printer',
            'pixma-ts3110': 'Canon PIXMA TS3110 Printer',
            'l3150': 'Epson EcoTank L3150 Printer',
            'l3210': 'Epson EcoTank L3210 Printer',
            'dcp-t510w': 'Brother DCP-T510W Printer',
            'mfc-t910dw': 'Brother MFC-T910DW Printer'
        }

    def print_banner(self):
        """Exibe banner do scanner"""
        print("=" * 120)
        print("🌐 NETWORK SCANNER - Organizado por Clientes (Sub-redes /24)")
        print("=" * 120)
        print(f"📡 Rede: {self.network}")
        print(f"🔍 IPs a escanear: {self.network.num_addresses}")
        print(f"🏢 Clientes esperados: {self.network.num_addresses // 254} (cada /24 = 1 cliente)")
        print(f"⚡ Threads: {self.max_workers}")
        print(f"⏱️  Timeout: {self.timeout}s")
        print(f"🕐 Início: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔬 Métodos: TTL Analysis, Port Scanning, Hostname Analysis, HTTP Fingerprinting")
        print(f"📱 Detecta: Windows, Linux, Android, iPhone, Roteadores, Firewalls, Impressoras, etc.")
        print("=" * 120)

    def ping_host(self, ip):
        """Verifica se o host está ativo via ping"""
        try:
            # Usar ping do sistema operacional
            if sys.platform.startswith('win'):
                cmd = ['ping', '-n', '1', '-w', str(self.timeout * 1000), str(ip)]
            else:
                cmd = ['ping', '-c', '1', '-W', str(self.timeout), str(ip)]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.timeout + 1)
            
            if result.returncode == 0:
                # Extrair TTL do ping para identificação de OS
                ttl = self.extract_ttl(result.stdout)
                return True, ttl
            return False, None
            
        except (subprocess.TimeoutExpired, Exception):
            return False, None

    def extract_ttl(self, ping_output):
        """Extrai TTL do output do ping"""
        try:
            if sys.platform.startswith('win'):
                # Windows: TTL=64
                for line in ping_output.split('\n'):
                    if 'TTL=' in line:
                        ttl = line.split('TTL=')[1].split()[0]
                        return int(ttl)
            else:
                # Linux: ttl=64
                for line in ping_output.split('\n'):
                    if 'ttl=' in line:
                        ttl = line.split('ttl=')[1].split()[0]
                        return int(ttl)
        except:
            pass
        return None

    def get_hostname(self, ip):
        """Tenta resolver o hostname do IP"""
        try:
            hostname = socket.gethostbyaddr(str(ip))[0]
            return hostname
        except:
            return None

    def scan_ports(self, ip, ports=None):
        """Escaneia portas específicas do host"""
        if ports is None:
            ports = list(self.common_ports.keys())

        open_ports = []
        for port in ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(0.5)
                result = sock.connect_ex((str(ip), port))
                if result == 0:
                    service = self.common_ports.get(port, f"Port-{port}")
                    open_ports.append((port, service))
                sock.close()
            except:
                pass

        return open_ports

    def identify_model_from_hostname(self, hostname):
        """Identifica modelo específico baseado no hostname"""
        if not hostname or hostname == "N/A":
            return None

        hostname_lower = hostname.lower()

        # Buscar por modelos conhecidos no hostname
        for model_key, model_name in self.known_models.items():
            if model_key in hostname_lower:
                return model_name

        # Buscar por padrões de modelo usando regex
        import re

        # Padrões para TP-Link
        tplink_patterns = [
            (r'archer[_-]?([a-z0-9]+)', 'TP-Link Archer {}'),
            (r'tl[_-]?wr([0-9]+[a-z]*)', 'TP-Link TL-WR{}'),
            (r'tl[_-]?([a-z]{2}[0-9]+[a-z]*)', 'TP-Link TL-{}'),
        ]

        # Padrões para D-Link
        dlink_patterns = [
            (r'dir[_-]?([0-9]+[a-z]*)', 'D-Link DIR-{}'),
            (r'dwr[_-]?([0-9]+[a-z]*)', 'D-Link DWR-{}'),
        ]

        # Padrões para Netgear
        netgear_patterns = [
            (r'r([0-9]+)', 'Netgear R{}'),
            (r'ax([0-9]+)', 'Netgear AX{}'),
        ]

        # Padrões para ASUS
        asus_patterns = [
            (r'rt[_-]?ac([0-9]+[a-z]*)', 'ASUS RT-AC{}'),
            (r'rt[_-]?ax([0-9]+[a-z]*)', 'ASUS RT-AX{}'),
        ]

        # Padrões para impressoras HP
        hp_patterns = [
            (r'deskjet[_-]?([0-9]+[a-z]*)', 'HP DeskJet {}'),
            (r'laserjet[_-]?([a-z0-9]+)', 'HP LaserJet {}'),
            (r'officejet[_-]?([0-9]+[a-z]*)', 'HP OfficeJet {}'),
        ]

        all_patterns = tplink_patterns + dlink_patterns + netgear_patterns + asus_patterns + hp_patterns

        for pattern, format_str in all_patterns:
            match = re.search(pattern, hostname_lower)
            if match:
                model = match.group(1).upper()
                return format_str.format(model)

        return None

    def http_fingerprint(self, ip, port=80):
        """Faz fingerprinting HTTP para obter informações do servidor"""
        try:
            import urllib.request
            import urllib.error

            url = f"http://{ip}:{port}/"
            req = urllib.request.Request(url)
            req.add_header('User-Agent', 'Mozilla/5.0 (compatible; NetworkScanner/1.0)')

            response = urllib.request.urlopen(req, timeout=3)
            headers = dict(response.headers)

            server_info = {
                'server': headers.get('Server', ''),
                'title': '',
                'model': ''
            }

            # Ler conteúdo para buscar título e modelo
            content = response.read(1024).decode('utf-8', errors='ignore')

            # Extrair título
            import re
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', content, re.IGNORECASE)
            if title_match:
                server_info['title'] = title_match.group(1).strip()

            # Buscar padrões de modelo em títulos e headers
            model_patterns = {
                r'TP-LINK\s+([A-Z0-9-]+)': 'TP-Link {}',
                r'D-Link\s+([A-Z0-9-]+)': 'D-Link {}',
                r'NETGEAR\s+([A-Z0-9-]+)': 'Netgear {}',
                r'ASUS\s+([A-Z0-9-]+)': 'ASUS {}',
                r'Linksys\s+([A-Z0-9-]+)': 'Linksys {}',
                r'UniFi\s+([A-Z0-9-]+)': 'UniFi {}',
                r'MikroTik\s+([A-Z0-9-]+)': 'MikroTik {}',
                r'pfSense': 'pfSense Firewall',
                r'OPNsense': 'OPNsense Firewall'
            }

            search_text = f"{server_info['title']} {server_info['server']}"
            for pattern, model_format in model_patterns.items():
                match = re.search(pattern, search_text, re.IGNORECASE)
                if match:
                    if '{}' in model_format:
                        server_info['model'] = model_format.format(match.group(1))
                    else:
                        server_info['model'] = model_format
                    break

            return server_info

        except:
            return None

    def identify_os(self, ttl, open_ports, hostname):
        """Identifica o sistema operacional baseado em várias características"""
        os_info = "Desconhecido"
        confidence = 0
        detection_methods = []

        # Identificação por TTL
        if ttl:
            for ttl_sig, os_name in self.os_signatures.items():
                if abs(ttl - ttl_sig) <= 5:  # Tolerância de ±5
                    os_info = os_name
                    confidence = 70
                    detection_methods.append(f"TTL={ttl}")
                    break

        # Refinamento baseado em portas abertas
        port_numbers = [port for port, _ in open_ports]

        if 445 in port_numbers or 135 in port_numbers or 3389 in port_numbers:
            if "Windows" not in os_info:
                os_info = "Windows"
                confidence = 80
                detection_methods.append("Ports")
            else:
                confidence = 90
                if "Ports" not in [m.split("=")[0] for m in detection_methods]:
                    detection_methods.append("Ports")

        if 22 in port_numbers and 80 in port_numbers:
            if ttl and 60 <= ttl <= 68:
                os_info = "Linux"
                confidence = 85
                if "Ports" not in [m.split("=")[0] for m in detection_methods]:
                    detection_methods.append("Ports")

        if 22 in port_numbers and ttl and ttl == 255:
            os_info = "Network Device (Router/Switch)"
            confidence = 90
            if "Ports" not in [m.split("=")[0] for m in detection_methods]:
                detection_methods.append("Ports")

        # Refinamento por hostname
        if hostname and hostname != "N/A":
            hostname_lower = hostname.lower()
            hostname_detected = False

            if any(x in hostname_lower for x in ['windows', 'win', 'pc', 'desktop']):
                if "Windows" not in os_info:
                    os_info = "Windows"
                confidence = max(confidence, 75)
                hostname_detected = True
            elif any(x in hostname_lower for x in ['linux', 'ubuntu', 'debian', 'centos', 'server']):
                if "Linux" not in os_info:
                    os_info = "Linux"
                confidence = max(confidence, 75)
                hostname_detected = True
            elif any(x in hostname_lower for x in ['router', 'switch', 'cisco', 'tp-link']):
                os_info = "Network Device"
                confidence = max(confidence, 80)
                hostname_detected = True

            if hostname_detected:
                detection_methods.append("Hostname")

        # Se não conseguiu identificar por nenhum método
        if not detection_methods:
            detection_methods.append("Unknown")

        detection_method = "+".join(detection_methods)

        return os_info, confidence, detection_method

    def get_device_type(self, open_ports, hostname, os_info, http_info=None):
        """Determina o tipo específico de dispositivo com modelo quando possível"""
        port_numbers = [port for port, _ in open_ports]
        hostname_lower = hostname.lower() if hostname and hostname != "N/A" else ""

        device_type = "Unknown Device"
        model = ""
        confidence = 0

        # 1. Identificação por modelo específico no hostname
        specific_model = self.identify_model_from_hostname(hostname)
        if specific_model:
            device_type = specific_model
            confidence = 95
        else:
            # 2. Identificação por padrões gerais de hostname
            for pattern, device_name in self.hostname_patterns.items():
                if pattern in hostname_lower:
                    device_type = device_name
                    confidence = 90
                    break

        # 3. Identificação por HTTP fingerprinting
        if confidence < 90 and http_info and http_info.get('model'):
            device_type = http_info['model']
            confidence = 95
        elif confidence < 90 and http_info and http_info.get('title'):
            title_lower = http_info['title'].lower()
            for pattern, device_name in self.hostname_patterns.items():
                if pattern in title_lower:
                    device_type = device_name
                    confidence = 85
                    break

        # 4. Identificação específica por portas e padrões
        if confidence < 80:
            # Dispositivos móveis (iOS/Android)
            if 62078 in port_numbers:  # iTunes sync
                device_type = "iPhone/iPad"
                confidence = 90
            elif 5353 in port_numbers and (60 <= self.extract_ttl_from_ping(hostname) <= 64):
                if "iphone" in hostname_lower or "ipad" in hostname_lower:
                    device_type = "Apple Device (iPhone/iPad)"
                else:
                    device_type = "Apple Device"
                confidence = 80
            elif 5353 in port_numbers and 1900 in port_numbers:
                device_type = "Android Device"
                confidence = 75

            # Roteadores e Access Points
            elif (80 in port_numbers or 443 in port_numbers) and 23 in port_numbers:
                device_type = "Router/Gateway"
                confidence = 85
            elif (80 in port_numbers or 443 in port_numbers) and any(p in port_numbers for p in [161, 162]):
                device_type = "Managed Switch/Router"
                confidence = 85
            elif 8080 in port_numbers or 8443 in port_numbers:
                device_type = "Access Point/Router"
                confidence = 80

            # Firewalls
            elif 443 in port_numbers and 22 in port_numbers and any(p in port_numbers for p in [161, 4433, 10443]):
                device_type = "Firewall/Security Appliance"
                confidence = 85

            # Impressoras
            elif 9100 in port_numbers:
                device_type = "Network Printer"
                confidence = 90
            elif 80 in port_numbers and any(x in hostname_lower for x in ['printer', 'print', 'hp', 'canon', 'epson']):
                device_type = "Network Printer"
                confidence = 85

            # Servidores
            elif 3389 in port_numbers:
                if 80 in port_numbers or 443 in port_numbers:
                    device_type = "Windows Server"
                else:
                    device_type = "Windows Desktop/Server"
                confidence = 90
            elif 22 in port_numbers and len([p for p in port_numbers if p > 1000]) >= 3:
                device_type = "Linux Server"
                confidence = 85
            elif 22 in port_numbers:
                device_type = "Linux/Unix System"
                confidence = 75

            # NAS e Storage
            elif any(x in hostname_lower for x in ['nas', 'synology', 'qnap', 'storage']):
                device_type = "NAS Device"
                confidence = 90

            # Smart devices
            elif 1900 in port_numbers and 80 in port_numbers:
                device_type = "Smart Device/Media Player"
                confidence = 70

            # Web servers genéricos
            elif 80 in port_numbers or 443 in port_numbers:
                device_type = "Web Server/Service"
                confidence = 60

            # Network devices genéricos
            elif any(p in port_numbers for p in [23, 161, 162]):
                device_type = "Network Device"
                confidence = 70

        # 5. Refinamento baseado no OS identificado
        if "Windows" in os_info and "Server" not in device_type:
            if 3389 in port_numbers:
                device_type = f"{device_type} (Windows Server)" if device_type != "Unknown Device" else "Windows Server"
            else:
                device_type = f"{device_type} (Windows)" if device_type != "Unknown Device" else "Windows Machine"
        elif "Linux" in os_info and "Server" not in device_type and "Router" not in device_type:
            device_type = f"{device_type} (Linux)" if device_type != "Unknown Device" else "Linux System"
        elif "macOS" in os_info or "iOS" in os_info:
            if device_type == "Unknown Device":
                device_type = "Apple Device"

        return device_type

    def extract_ttl_from_ping(self, hostname):
        """Extrai TTL fazendo ping no hostname (método auxiliar)"""
        try:
            if hostname and hostname != "N/A":
                is_alive, ttl = self.ping_host(hostname)
                return ttl if ttl else 0
        except:
            pass
        return 0

    def scan_host(self, ip):
        """Escaneia um host específico"""
        try:
            # Ping test
            is_alive, ttl = self.ping_host(ip)
            
            if not is_alive:
                return None
            
            # Resolver hostname
            hostname = self.get_hostname(ip)
            
            # Scan de portas
            open_ports = self.scan_ports(ip)

            # HTTP fingerprinting se porta 80 estiver aberta
            http_info = None
            port_numbers = [port for port, _ in open_ports]
            if 80 in port_numbers:
                http_info = self.http_fingerprint(ip, 80)
            elif 443 in port_numbers:
                http_info = self.http_fingerprint(ip, 443)
            elif 8080 in port_numbers:
                http_info = self.http_fingerprint(ip, 8080)

            # Identificar OS
            os_info, confidence, detection_method = self.identify_os(ttl, open_ports, hostname)

            # Determinar tipo de dispositivo
            device_type = self.get_device_type(open_ports, hostname, os_info, http_info)

            result = {
                'ip': str(ip),
                'hostname': hostname or 'N/A',
                'ttl': ttl,
                'os': os_info,
                'confidence': confidence,
                'detection_method': detection_method,
                'device_type': device_type,
                'open_ports': open_ports,
                'http_info': http_info,
                'scan_time': datetime.now().isoformat()
            }
            
            with self.lock:
                self.results.append(result)
                # Truncar strings longas para caber na tabela
                hostname_short = (hostname or 'N/A')[:20]
                os_short = os_info[:15]
                method_short = detection_method[:12]
                device_short = device_type[:35]

                print(f"✅ {str(ip):<15} | {hostname_short:<20} | {os_short:<15} | {method_short:<12} | {device_short:<35}")

            return result
            
        except Exception as e:
            print(f"❌ Erro ao escanear {str(ip)}: {e}")
            return None

    def scan_network(self):
        """Escaneia toda a rede organizando por clientes (sub-redes /24)"""
        self.print_banner()

        start_time = time.time()

        # Organizar IPs por sub-rede /24 (clientes)
        clients = {}
        for ip in self.network.hosts():
            # Extrair os primeiros 3 octetos para identificar o cliente
            ip_parts = str(ip).split('.')
            client_network = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}"
            client_id = int(ip_parts[2])  # Terceiro octeto como ID do cliente

            if client_network not in clients:
                clients[client_network] = {
                    'id': client_id,
                    'ips': [],
                    'results': []
                }
            clients[client_network]['ips'].append(ip)

        print(f"🏢 Detectados {len(clients)} clientes (sub-redes /24)")
        print(f"⚡ Escaneando com {self.max_workers} threads...")
        print("=" * 120)

        # Escanear todos os IPs
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {executor.submit(self.scan_host, ip): ip for ip in self.network.hosts()}

            for future in as_completed(futures):
                try:
                    result = future.result()
                    if result:
                        # Organizar resultado por cliente
                        ip_parts = result['ip'].split('.')
                        client_network = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}"
                        if client_network in clients:
                            clients[client_network]['results'].append(result)
                except Exception as e:
                    ip = futures[future]
                    print(f"❌ Erro ao processar {str(ip)}: {e}")

        end_time = time.time()

        # Exibir resultados organizados por cliente
        self.print_client_results(clients, end_time - start_time)

    def print_client_results(self, clients, scan_time):
        """Exibe resultados organizados por cliente"""
        print("\n" + "=" * 120)
        print("📊 RESULTADOS POR CLIENTE")
        print("=" * 120)

        total_devices = 0
        clients_with_devices = 0

        # Ordenar clientes por ID
        sorted_clients = sorted(clients.items(), key=lambda x: x[1]['id'])

        for client_network, client_data in sorted_clients:
            client_results = client_data['results']
            client_id = client_data['id']

            if client_results:
                clients_with_devices += 1
                total_devices += len(client_results)

                print(f"\n🏢 CLIENTE {client_id:03d} - Rede: {client_network}.0/24")
                print(f"📱 Dispositivos encontrados: {len(client_results)}")
                print("-" * 120)
                print(f"{'IP':<15} | {'Hostname':<20} | {'OS':<15} | {'Método':<12} | {'Dispositivo/Modelo':<35}")
                print("-" * 120)

                # Ordenar por último octeto do IP
                sorted_results = sorted(client_results, key=lambda x: int(x['ip'].split('.')[-1]))

                for result in sorted_results:
                    hostname_short = result['hostname'][:20]
                    os_short = result['os'][:15]
                    method_short = result['detection_method'][:12]
                    device_short = result['device_type'][:35]

                    print(f"✅ {result['ip']:<15} | {hostname_short:<20} | {os_short:<15} | {method_short:<12} | {device_short:<35}")

                # Estatísticas do cliente
                client_os_stats = {}
                client_device_stats = {}

                for result in client_results:
                    os_name = result['os']
                    device_type = result['device_type']
                    client_os_stats[os_name] = client_os_stats.get(os_name, 0) + 1
                    client_device_stats[device_type] = client_device_stats.get(device_type, 0) + 1

                print(f"\n📊 Estatísticas Cliente {client_id:03d}:")
                print(f"   🖥️  OS: {', '.join([f'{os}({count})' for os, count in client_os_stats.items()])}")
                print(f"   📱 Dispositivos: {', '.join([f'{dev}({count})' for dev, count in list(client_device_stats.items())[:3]])}")
                if len(client_device_stats) > 3:
                    print(f"      ... e mais {len(client_device_stats) - 3} tipos")

        # Resumo geral
        self.print_general_summary(clients, total_devices, clients_with_devices, scan_time)

    def print_general_summary(self, clients, total_devices, clients_with_devices, scan_time):
        """Exibe resumo geral do scan"""
        print("\n" + "=" * 120)
        print("📈 RESUMO GERAL")
        print("=" * 120)
        print(f"⏱️  Tempo total de scan: {scan_time:.2f} segundos")
        print(f"🏢 Total de clientes (sub-redes): {len(clients)}")
        print(f"📱 Clientes com dispositivos: {clients_with_devices}")
        print(f"🎯 Total de dispositivos encontrados: {total_devices}")
        print(f"📡 Total de IPs escaneados: {self.network.num_addresses}")

        # Estatísticas globais
        all_results = []
        for client_data in clients.values():
            all_results.extend(client_data['results'])

        if all_results:
            # Top 5 clientes com mais dispositivos
            client_counts = {}
            for client_network, client_data in clients.items():
                if client_data['results']:
                    client_id = client_data['id']
                    client_counts[f"Cliente {client_id:03d} ({client_network}.0/24)"] = len(client_data['results'])

            if client_counts:
                print(f"\n🏆 TOP 5 CLIENTES (mais dispositivos):")
                top_clients = sorted(client_counts.items(), key=lambda x: x[1], reverse=True)[:5]
                for i, (client_name, count) in enumerate(top_clients, 1):
                    print(f"   {i}. {client_name}: {count} dispositivos")

            # Estatísticas globais de OS
            global_os_stats = {}
            global_device_stats = {}
            global_method_stats = {}

            for result in all_results:
                os_name = result['os']
                device_type = result['device_type']
                method = result['detection_method']

                global_os_stats[os_name] = global_os_stats.get(os_name, 0) + 1
                global_device_stats[device_type] = global_device_stats.get(device_type, 0) + 1
                global_method_stats[method] = global_method_stats.get(method, 0) + 1

            print(f"\n🖥️  SISTEMAS OPERACIONAIS (Global):")
            for os_name, count in sorted(global_os_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_devices) * 100
                print(f"   {os_name}: {count} ({percentage:.1f}%)")

            print(f"\n📱 TIPOS DE DISPOSITIVOS (Top 10):")
            top_devices = sorted(global_device_stats.items(), key=lambda x: x[1], reverse=True)[:10]
            for device_type, count in top_devices:
                percentage = (count / total_devices) * 100
                print(f"   {device_type}: {count} ({percentage:.1f}%)")

            print(f"\n🔍 MÉTODOS DE DETECÇÃO:")
            for method, count in sorted(global_method_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_devices) * 100
                print(f"   {method}: {count} ({percentage:.1f}%)")

    def print_summary(self, scan_time):
        """Exibe resumo do scan"""
        print("\n" + "=" * 120)
        print("📊 RESUMO DO SCAN AVANÇADO")
        print("=" * 120)
        print(f"⏱️  Tempo total: {scan_time:.2f} segundos")
        print(f"🎯 Hosts encontrados: {len(self.results)}")
        print(f"📡 Total de IPs escaneados: {self.network.num_addresses}")
        
        # Estatísticas por OS, dispositivos e métodos de detecção
        os_stats = {}
        device_stats = {}
        method_stats = {}

        for result in self.results:
            os_name = result['os']
            device_type = result['device_type']
            detection_method = result['detection_method']

            os_stats[os_name] = os_stats.get(os_name, 0) + 1
            device_stats[device_type] = device_stats.get(device_type, 0) + 1
            method_stats[detection_method] = method_stats.get(detection_method, 0) + 1

        print("\n🖥️  Sistemas Operacionais encontrados:")
        for os_name, count in sorted(os_stats.items()):
            print(f"   {os_name}: {count}")

        print("\n📱 Tipos de dispositivos encontrados:")
        for device_type, count in sorted(device_stats.items()):
            print(f"   {device_type}: {count}")

        print("\n🔍 Métodos de detecção utilizados:")
        for method, count in sorted(method_stats.items()):
            print(f"   {method}: {count}")

    def save_results(self, filename=None):
        """Salva resultados em arquivo JSON"""
        if filename is None:
            filename = f"network_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'scan_info': {
                    'network': str(self.network),
                    'scan_time': datetime.now().isoformat(),
                    'total_hosts': len(self.results)
                },
                'results': self.results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Resultados salvos em: {filename}")

    def save_to_excel(self, filename=None):
        """Salva resultados em arquivo Excel (.xlsx) organizados por cliente"""
        if not EXCEL_AVAILABLE:
            print("❌ Erro: openpyxl não está instalado. Execute: pip install openpyxl")
            return False

        if filename is None:
            filename = f"network_scan_clientes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        try:
            # Organizar resultados por cliente
            clients = {}
            for result in self.results:
                ip_parts = result['ip'].split('.')
                client_network = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}"
                client_id = int(ip_parts[2])

                if client_network not in clients:
                    clients[client_network] = {
                        'id': client_id,
                        'results': []
                    }
                clients[client_network]['results'].append(result)

            # Criar workbook
            wb = openpyxl.Workbook()

            # Aba principal - Todos os dispositivos
            ws_all = wb.active
            ws_all.title = "Todos os Dispositivos"

            # Cabeçalhos
            headers = [
                "Cliente", "IP", "Hostname", "Sistema Operacional", "Método Detecção",
                "Tipo/Modelo", "TTL", "Confiança (%)", "Portas Abertas",
                "Data/Hora Scan"
            ]

            # Estilizar cabeçalho
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")

            for col, header in enumerate(headers, 1):
                cell = ws_all.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment

            # Adicionar dados de todos os clientes
            row = 2
            for client_network, client_data in sorted(clients.items(), key=lambda x: x[1]['id']):
                client_id = client_data['id']
                for result in sorted(client_data['results'], key=lambda x: int(x['ip'].split('.')[-1])):
                    ws_all.cell(row=row, column=1, value=f"Cliente {client_id:03d}")
                    ws_all.cell(row=row, column=2, value=result['ip'])
                    ws_all.cell(row=row, column=3, value=result['hostname'])
                    ws_all.cell(row=row, column=4, value=result['os'])
                    ws_all.cell(row=row, column=5, value=result['detection_method'])
                    ws_all.cell(row=row, column=6, value=result['device_type'])
                    ws_all.cell(row=row, column=7, value=result['ttl'])
                    ws_all.cell(row=row, column=8, value=result['confidence'])

                    # Formatar portas abertas
                    ports_str = ", ".join([f"{port}({service})" for port, service in result['open_ports']])
                    ws_all.cell(row=row, column=9, value=ports_str)

                    # Data/hora formatada
                    scan_time = datetime.fromisoformat(result['scan_time']).strftime('%d/%m/%Y %H:%M:%S')
                    ws_all.cell(row=row, column=10, value=scan_time)
                    row += 1

            # Ajustar largura das colunas
            column_widths = [12, 15, 25, 20, 15, 35, 8, 12, 30, 20]
            for col, width in enumerate(column_widths, 1):
                ws_all.column_dimensions[get_column_letter(col)].width = width

            # Criar abas individuais para cada cliente (top 10 clientes com mais dispositivos)
            client_counts = [(client_network, client_data) for client_network, client_data in clients.items() if client_data['results']]
            client_counts.sort(key=lambda x: len(x[1]['results']), reverse=True)

            for client_network, client_data in client_counts[:10]:  # Top 10 clientes
                client_id = client_data['id']
                client_results = client_data['results']

                # Criar aba para o cliente
                ws_client = wb.create_sheet(f"Cliente {client_id:03d}")

                # Cabeçalhos do cliente
                client_headers = ["IP", "Hostname", "OS", "Método", "Tipo/Modelo", "TTL", "Confiança", "Portas"]
                for col, header in enumerate(client_headers, 1):
                    cell = ws_client.cell(row=1, column=col, value=header)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment

                # Adicionar dados do cliente
                for row, result in enumerate(sorted(client_results, key=lambda x: int(x['ip'].split('.')[-1])), 2):
                    ws_client.cell(row=row, column=1, value=result['ip'])
                    ws_client.cell(row=row, column=2, value=result['hostname'])
                    ws_client.cell(row=row, column=3, value=result['os'])
                    ws_client.cell(row=row, column=4, value=result['detection_method'])
                    ws_client.cell(row=row, column=5, value=result['device_type'])
                    ws_client.cell(row=row, column=6, value=result['ttl'])
                    ws_client.cell(row=row, column=7, value=result['confidence'])

                    ports_str = ", ".join([f"{port}" for port, _ in result['open_ports']])
                    ws_client.cell(row=row, column=8, value=ports_str)

                # Ajustar larguras da aba do cliente
                client_widths = [15, 25, 15, 12, 30, 8, 10, 25]
                for col, width in enumerate(client_widths, 1):
                    ws_client.column_dimensions[get_column_letter(col)].width = width

            # Aba de estatísticas por cliente
            ws_clients = wb.create_sheet("Estatísticas Clientes")

            # Cabeçalhos estatísticas clientes
            client_headers = ["Cliente", "Rede", "Dispositivos", "OS Principal", "Dispositivo Principal"]
            for col, header in enumerate(client_headers, 1):
                cell = ws_clients.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill

            # Adicionar estatísticas por cliente
            for row, (client_network, client_data) in enumerate(sorted(clients.items(), key=lambda x: x[1]['id']), 2):
                if client_data['results']:
                    client_id = client_data['id']
                    client_results = client_data['results']

                    # Calcular OS principal
                    client_os_stats = {}
                    client_device_stats = {}
                    for result in client_results:
                        os_name = result['os']
                        device_type = result['device_type']
                        client_os_stats[os_name] = client_os_stats.get(os_name, 0) + 1
                        client_device_stats[device_type] = client_device_stats.get(device_type, 0) + 1

                    main_os = max(client_os_stats.items(), key=lambda x: x[1])[0] if client_os_stats else "N/A"
                    main_device = max(client_device_stats.items(), key=lambda x: x[1])[0] if client_device_stats else "N/A"

                    ws_clients.cell(row=row, column=1, value=f"Cliente {client_id:03d}")
                    ws_clients.cell(row=row, column=2, value=f"{client_network}.0/24")
                    ws_clients.cell(row=row, column=3, value=len(client_results))
                    ws_clients.cell(row=row, column=4, value=main_os)
                    ws_clients.cell(row=row, column=5, value=main_device[:30])

            # Aba de estatísticas - Sistemas Operacionais
            ws_os = wb.create_sheet("Estatísticas OS")

            # Cabeçalhos estatísticas OS
            ws_os.cell(row=1, column=1, value="Sistema Operacional").font = header_font
            ws_os.cell(row=1, column=1).fill = header_fill
            ws_os.cell(row=1, column=2, value="Quantidade").font = header_font
            ws_os.cell(row=1, column=2).fill = header_fill
            ws_os.cell(row=1, column=3, value="Percentual").font = header_font
            ws_os.cell(row=1, column=3).fill = header_fill

            # Calcular estatísticas OS
            os_stats = {}
            for result in self.results:
                os_name = result['os']
                os_stats[os_name] = os_stats.get(os_name, 0) + 1

            total_devices = len(self.results)
            for row, (os_name, count) in enumerate(sorted(os_stats.items()), 2):
                percentage = (count / total_devices) * 100
                ws_os.cell(row=row, column=1, value=os_name)
                ws_os.cell(row=row, column=2, value=count)
                ws_os.cell(row=row, column=3, value=f"{percentage:.1f}%")

            # Aba de estatísticas - Tipos de Dispositivos
            ws_devices_stats = wb.create_sheet("Estatísticas Dispositivos")

            # Cabeçalhos estatísticas dispositivos
            ws_devices_stats.cell(row=1, column=1, value="Tipo de Dispositivo").font = header_font
            ws_devices_stats.cell(row=1, column=1).fill = header_fill
            ws_devices_stats.cell(row=1, column=2, value="Quantidade").font = header_font
            ws_devices_stats.cell(row=1, column=2).fill = header_fill
            ws_devices_stats.cell(row=1, column=3, value="Percentual").font = header_font
            ws_devices_stats.cell(row=1, column=3).fill = header_fill

            # Calcular estatísticas dispositivos
            device_stats = {}
            for result in self.results:
                device_type = result['device_type']
                device_stats[device_type] = device_stats.get(device_type, 0) + 1

            for row, (device_type, count) in enumerate(sorted(device_stats.items()), 2):
                percentage = (count / total_devices) * 100
                ws_devices_stats.cell(row=row, column=1, value=device_type)
                ws_devices_stats.cell(row=row, column=2, value=count)
                ws_devices_stats.cell(row=row, column=3, value=f"{percentage:.1f}%")

            # Aba de métodos de detecção
            ws_methods = wb.create_sheet("Métodos Detecção")

            # Cabeçalhos métodos
            ws_methods.cell(row=1, column=1, value="Método de Detecção").font = header_font
            ws_methods.cell(row=1, column=1).fill = header_fill
            ws_methods.cell(row=1, column=2, value="Quantidade").font = header_font
            ws_methods.cell(row=1, column=2).fill = header_fill
            ws_methods.cell(row=1, column=3, value="Percentual").font = header_font
            ws_methods.cell(row=1, column=3).fill = header_fill

            # Calcular estatísticas métodos
            method_stats = {}
            for result in self.results:
                method = result['detection_method']
                method_stats[method] = method_stats.get(method, 0) + 1

            for row, (method, count) in enumerate(sorted(method_stats.items()), 2):
                percentage = (count / total_devices) * 100
                ws_methods.cell(row=row, column=1, value=method)
                ws_methods.cell(row=row, column=2, value=count)
                ws_methods.cell(row=row, column=3, value=f"{percentage:.1f}%")

            # Aba de informações do scan
            ws_info = wb.create_sheet("Informações do Scan")

            # Informações gerais
            info_data = [
                ("Rede Escaneada", str(self.network)),
                ("Total de IPs", self.network.num_addresses),
                ("Dispositivos Encontrados", len(self.results)),
                ("Threads Utilizadas", self.max_workers),
                ("Timeout (segundos)", self.timeout),
                ("Data/Hora do Scan", datetime.now().strftime('%d/%m/%Y %H:%M:%S')),
                ("Desenvolvido por", "Paulo Matheus")
            ]

            for row, (label, value) in enumerate(info_data, 1):
                ws_info.cell(row=row, column=1, value=label).font = Font(bold=True)
                ws_info.cell(row=row, column=2, value=value)

            # Ajustar larguras das abas de estatísticas
            for ws in [ws_os, ws_devices_stats, ws_methods]:
                ws.column_dimensions['A'].width = 30
                ws.column_dimensions['B'].width = 12
                ws.column_dimensions['C'].width = 12

            ws_info.column_dimensions['A'].width = 25
            ws_info.column_dimensions['B'].width = 30

            # Salvar arquivo
            wb.save(filename)
            print(f"\n📊 Resultados exportados para Excel: {filename}")
            print(f"📋 Abas criadas:")
            print(f"   • Todos os Dispositivos ({len(self.results)} dispositivos)")
            print(f"   • Estatísticas Clientes ({len([c for c in clients.values() if c['results']])} clientes)")
            print(f"   • Estatísticas OS ({len(os_stats)} tipos)")
            print(f"   • Estatísticas Dispositivos ({len(device_stats)} tipos)")
            print(f"   • Métodos Detecção ({len(method_stats)} métodos)")
            print(f"   • Informações do Scan")

            # Mostrar abas de clientes criadas
            client_tabs = min(10, len([c for c in clients.values() if c['results']]))
            if client_tabs > 0:
                print(f"   • {client_tabs} abas individuais de clientes (top {client_tabs})")

            return True

        except Exception as e:
            print(f"❌ Erro ao salvar Excel: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='Network Scanner - Identifica dispositivos na rede')
    parser.add_argument('--network', '-n', default='***********/16',
                       help='Rede para escanear (padrão: ***********/16)')
    parser.add_argument('--threads', '-t', type=int, default=100,
                       help='Número de threads (padrão: 100)')
    parser.add_argument('--timeout', type=int, default=2,
                       help='Timeout em segundos (padrão: 2)')
    parser.add_argument('--save', '-s', action='store_true',
                       help='Salvar resultados em arquivo JSON')
    parser.add_argument('--excel', '-x', action='store_true',
                       help='Exportar resultados para Excel (.xlsx)')
    parser.add_argument('--output', '-o', type=str,
                       help='Nome do arquivo de saída (sem extensão)')
    
    args = parser.parse_args()

    # Aviso para redes grandes
    try:
        import ipaddress
        network_obj = ipaddress.IPv4Network(args.network)
        if network_obj.num_addresses > 1000:
            print(f"⚠️  AVISO: Scan de {network_obj.num_addresses} IPs pode demorar muito tempo!")
            print(f"📡 Rede: {args.network}")
            print(f"⚡ Threads: {args.threads}")
            print(f"⏱️  Tempo estimado: {network_obj.num_addresses // args.threads // 10} - {network_obj.num_addresses // args.threads // 5} minutos")
            confirm = input("Deseja continuar? (s/N): ")
            if not confirm.lower().startswith('s'):
                print("Scan cancelado.")
                return
    except:
        pass

    try:
        scanner = NetworkScanner(
            network=args.network,
            max_workers=args.threads,
            timeout=args.timeout
        )
        
        scanner.scan_network()

        # Salvar resultados conforme solicitado
        if args.save:
            if args.output:
                filename = f"{args.output}.json"
                scanner.save_results(filename)
            else:
                scanner.save_results()

        if args.excel:
            if not EXCEL_AVAILABLE:
                print("\n❌ Para exportar Excel, instale: pip install openpyxl")
            else:
                if args.output:
                    filename = f"{args.output}.xlsx"
                    scanner.save_to_excel(filename)
                else:
                    scanner.save_to_excel()

    except KeyboardInterrupt:
        print("\n\n⚠️  Scan interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro durante o scan: {e}")

if __name__ == "__main__":
    main()
