# Raspberry Pi Plug and Play - Zabbix + Tactical RMM

## Descrição

Solução de automação para Raspberry Pi que instala e configura automaticamente **Zabbix Proxy**, **Zabbix Agent** e **Tactical RMM** no primeiro boot, criando um sistema plug-and-play para monitoramento.

## Características

- ✅ **Instalação 100% automática** no primeiro boot
- ✅ **Configuração pré-definida** (CLIENT_ID=1, SITE_ID=1, hostname="MUDAR")
- ✅ **Plug and Play** - apenas conectar à internet e ligar
- ✅ **Compatível com Raspberry Pi** (ARM64/ARM32)
- ✅ **Configuração de rede automática**
- ✅ **Firewall básico configurado**

## Arquivos do Projeto

| Arquivo | Descrição |
|---------|-----------|
| `setup_first_boot.sh` | Script principal executado no primeiro boot |
| `install_first_boot_service.sh` | Configura o serviço systemd para execução automática |
| `prepare_raspberry_image.sh` | Prepara a imagem base do Raspberry Pi |
| `README_RASPBERRY_AUTOMATION.md` | Este arquivo de documentação |

## Como Usar

### 1. Preparar a Imagem Base

```bash
# Em um Raspberry Pi limpo com Raspberry Pi OS
sudo ./prepare_raspberry_image.sh
```

Este script irá:
- Atualizar o sistema
- Instalar dependências
- Configurar o serviço de primeiro boot
- Preparar o sistema para clonagem

### 2. Criar a Imagem

```bash
# Desligar o Raspberry Pi
sudo shutdown -h now

# Clone o SD card usando ferramentas como:
# - Raspberry Pi Imager
# - dd command
# - Win32DiskImager
```

### 3. Usar a Imagem

1. **Grave a imagem** em um novo SD card
2. **Insira no Raspberry Pi** e conecte à internet
3. **Ligue o sistema** e aguarde 10-15 minutos
4. **Verifique a instalação** com `check-setup`
5. **Acesse o Zabbix** em `monitora.nvirtual.com.br` e altere o nome do proxy

## Configurações Padrão

| Parâmetro | Valor Padrão |
|-----------|--------------|
| **Zabbix Hostname** | `MUDAR` |
| **Tactical Client ID** | `1` |
| **Tactical Site ID** | `1` |
| **Zabbix Server** | `monitora.nvirtual.com.br` |
| **Tactical Mesh** | `mesh.centralmesh.nvirtual.com.br` |
| **Tactical API** | `api.centralmesh.nvirtual.com.br` |

## Processo de Instalação Automática

### No Primeiro Boot:

1. **Aguarda conectividade** de rede (até 60 segundos)
2. **Baixa o script** do GitHub com token de autenticação
3. **Modifica o script** para execução não-interativa
4. **Instala e configura**:
   - Zabbix Proxy SQLite3
   - Zabbix Agent
   - Tactical RMM Agent
5. **Configura firewall** (portas 22, 10050, 10051, 80, 443)
6. **Configura rede** (IP fixo .222 ou DHCP se ocupado)
7. **Cria arquivos informativos**
8. **Marca instalação como concluída**

## Comandos Úteis

```bash
# Verificar status da instalação
check-setup

# Ver logs da instalação em tempo real
sudo journalctl -u first-boot-setup.service -f

# Ver informações do sistema
cat /home/<USER>/SISTEMA_INFO.txt

# Verificar serviços Zabbix
sudo systemctl status zabbix-proxy
sudo systemctl status zabbix-agent

# Ver logs do Zabbix
sudo tail -f /var/log/zabbix/zabbix_proxy.log
sudo tail -f /var/log/zabbix/zabbix_agentd.log
```

## Estrutura de Arquivos Criados

```
/home/<USER>/
├── IMAGEM_INFO.txt              # Informações da imagem base
├── INSTRUCOES_PRIMEIRO_BOOT.txt # Instruções para o usuário
├── SISTEMA_INFO.txt             # Informações pós-instalação
└── .first_boot_completed        # Flag de instalação concluída

/usr/local/bin/
├── setup_first_boot.sh          # Script principal
└── check_first_boot_status.sh   # Script de verificação

/etc/systemd/system/
└── first-boot-setup.service     # Serviço systemd
```

## Personalização

### Alterar Configurações Padrão

Edite o arquivo `setup_first_boot.sh`:

```bash
# Configurações padrão (plug and play)
ZABBIX_HOSTNAME="SEU_HOSTNAME"
TACTICAL_CLIENT_ID="SEU_CLIENT_ID"
TACTICAL_CLIENT_FILIAL="SEU_SITE_ID"
```

### Alterar Token do GitHub

Edite a variável no `setup_first_boot.sh`:

```bash
GITHUB_TOKEN="seu_novo_token_aqui"
```

## Troubleshooting

### Verificar se a Instalação Foi Executada

```bash
check-setup
```

### Instalação Não Iniciou

```bash
# Verificar se o serviço está habilitado
sudo systemctl is-enabled first-boot-setup.service

# Executar manualmente
sudo systemctl start first-boot-setup.service
```

### Problemas de Conectividade

```bash
# Testar conectividade
ping -c 3 *******

# Verificar configuração de rede
ip addr show
```

### Serviços Zabbix Não Iniciaram

```bash
# Verificar logs
sudo journalctl -u zabbix-proxy -f
sudo journalctl -u zabbix-agent -f

# Reiniciar serviços
sudo systemctl restart zabbix-proxy
sudo systemctl restart zabbix-agent
```

## Requisitos

- **Raspberry Pi** com Raspberry Pi OS
- **Conexão com internet** no primeiro boot
- **Acesso ao GitHub** (para download do script)
- **Aproximadamente 15 minutos** para instalação completa

## Segurança

- Firewall configurado automaticamente
- SSH habilitado por padrão
- Senhas padrão devem ser alteradas
- Token do GitHub com acesso limitado

## Suporte

**Desenvolvido por:** Paulo Matheus - NVirtual

Para suporte ou dúvidas, consulte os logs do sistema ou entre em contato com a equipe de desenvolvimento.

## Changelog

### v1.0.0
- Implementação inicial
- Suporte para Raspberry Pi ARM64/ARM32
- Instalação automática de Zabbix 7.0
- Integração com Tactical RMM
- Configuração automática de rede e firewall
