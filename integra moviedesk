import re
import smtplib
import pandas as pd
import requests
import datetime
from email.message import EmailMessage
from collections import defaultdict

SMTP_SERVER = "smtp.skymail.net.br"
SMTP_PORT = 465
SMTP_USER = "<EMAIL>"
SMTP_PASSWORD = "nwjdCoBE3P"
FROM_EMAIL = SMTP_USER
MOVIEDESK_TOKEN = "6defc4f4-2b44-4ff9-9b3b-b8bb933ee91e"
MOVIEDESK_URL = f"https://api.movidesk.com/public/v1/tickets?token={MOVIEDESK_TOKEN}"

PLAINTEXT_TEMPLATE = """Olá {nome_cliente},

Você está recebendo uma notificação da área de segurança da Nmail.

Nosso sistema identificou que as seguintes contas estão com senha fraca:
{lista_emails}

Solicitamos a alteração para um padrão mais complexo.

Atenciosamente,
Equipe de Segurança Nmail.
"""

HTML_TEMPLATE = """\
<html>
  <body style="font-family: Arial, sans-serif; color: #333;">
    <div style="text-align: center; margin-bottom: 20px;">
      <img src="https://img.icons8.com/color/48/000000/security-checked.png" alt="Segurança" style="width:48px;height:48px;">
      <h2>Aviso de Segurança</h2>
    </div>
    <p>Olá <strong>{nome_cliente}</strong>,</p>
    <p>Você está recebendo uma notificação da área de segurança da <strong>Nmail</strong>.</p>
    <p>
      Nosso sistema de segurança identificou que as seguintes contas (colaborador(es)) estão com senha de baixa complexidade (Senhas Fracas):
    </p>
    <ul>
      {lista_emails_li}
    </ul>
    <p>
      Para que a segurança dessas contas e dos nossos sistemas seja preservada, solicitamos que seja alterada para um padrão mais complexo.
    </p>
    <h3>Dicas para criar uma senha forte e segura:</h3>
    <ul>
      <li>
        <img src="https://img.icons8.com/color/20/000000/lock.png" alt="Ícone" style="vertical-align: middle;">
        Tamanho: Use entre 10 e 15 caracteres.
      </li>
      <li>
        <img src="https://img.icons8.com/color/20/000000/lock.png" alt="Ícone" style="vertical-align: middle;">
        Variedade: Misture letras maiúsculas, minúsculas, números e símbolos especiais.
      </li>
      <li>
        <img src="https://img.icons8.com/color/20/000000/lock.png" alt="Ícone" style="vertical-align: middle;">
        Senhas distintas: Evite usar a mesma senha para diferentes contas.
      </li>
      <li>
        <img src="https://img.icons8.com/color/20/000000/lock.png" alt="Ícone" style="vertical-align: middle;">
        Nada óbvio: Não utilize informações pessoais, como datas de nascimento ou nomes.
      </li>
      <li>
        <img src="https://img.icons8.com/color/20/000000/lock.png" alt="Ícone" style="vertical-align: middle;">
        Teste de força: Utilize ferramentas para teste de senha e escreva sua senha em <a href="https://www.security.org/how-secure-is-my-password/" target="_blank">“How Secure Is My Password?”</a> para avaliar sua nova senha. Caso sua Senha fique Verde se trata de uma senha segura.
      </li>
      <li>
      <img src="https://img.icons8.com/color/20/000000/lock.png" alt="Ícone de segurança" style="vertical-align: middle; margin-right: 5px;">
      Gerenciador: Considere usar um gerenciador de senhas para criar e armazenar suas senhas com segurança.
      Recomendamos 
      <img src="https://img.icons8.com/color/20/000000/lastpass.png" alt="LastPass" style="vertical-align: middle; margin: 0 5px;">
      <a href="https://www.lastpass.com/pt" target="_blank">LastPass</a>,
      <img src="https://img.icons8.com/color/20/000000/bitwarden.png" alt="Bitwarden" style="vertical-align: middle; margin: 0 5px;">
      <a href="https://bitwarden.com/" target="_blank">Bitwarden</a> ou 
      <img src="https://img.icons8.com/color/20/000000/1password.png" alt="1Password" style="vertical-align: middle; margin: 0 5px;">
      <a href="https://1password.com/" target="_blank">1Password</a>
    </li>
      <li>
        <img src="https://img.icons8.com/color/20/000000/lock.png" alt="Ícone" style="vertical-align: middle;">
        Se sente sem criatividade? Utilize ferramentas para criar senhas automaticamente <a href="https://www.keepersecurity.com/pt_BR/features/password-generator.html" target="_blank">“Clicando Aqui”</a> e guarde-a de forma segura.
      </li>
    </ul>
    <h3>Como alterar sua senha de e-mail:</h3>
    <ol>
      <li>Acesse seu e-mail e clique nos três pontinhos no canto inferior direito.</li>
      <li>Selecione <strong>Modificar Senha</strong>.</li>
      <li>Digite sua senha atual, a nova senha duas vezes e clique em <strong>Salvar</strong>.</li>
    </ol>
    <p>
      Para mais informações, consulte nossa <a href="https://sistema.nvirtual.com.br/kb/pt-br/article/376780/nmail-alterar-senha" target="_blank">documentação</a>.
    </p>
    <p>
      Lembre-se dos requisitos para uma nova senha:<br>
      • Pelo menos 8 caracteres;<br>
      • Não incluir informações relacionadas ao seu e-mail;<br>
      • Evitar sequências numéricas ou alfabéticas (como "12345" ou "abcd");<br>
      • Incluir pelo menos um número e um caractere especial.
    </p>
    <p>
      Caso não saiba a senha atual, entre em contato com nosso suporte para assistência.
    </p>
    <p>
      Atenciosamente,<br>
      Equipe de Segurança Nmail.
    </p>
  </body>
</html>
"""

def extrair_emails(texto):
    padrao = r"[\w._%+-]+@[\w.-]+\.[a-zA-Z]{2,}"
    return re.findall(padrao, texto)

def agrupar_por_dominio(emails):
    grupos = defaultdict(list)
    for email in emails:
        dominio = email.split('@')[1].lower()
        grupos[dominio].append(email)
    return grupos

def carregar_responsaveis(arquivo):
    df = pd.read_excel(arquivo)
    responsaveis = {}
    for _, row in df.iterrows():
        email_resp = str(row['E-mail']).strip()
        if email_resp:
            dominio = email_resp.split('@')[1].lower()
            responsaveis[dominio] = email_resp
    return responsaveis

def buscar_usuario_movidesk_por_email(email):
    url = f"https://api.movidesk.com/public/v1/persons"
    params = {
        "token": MOVIEDESK_TOKEN,
        "$filter": f"Emails/any(e: e/email eq '{email}')"
    }
    response = requests.get(url, params=params)
    if response.status_code == 200 and response.json():
        usuario = response.json()[0]
        print(f"Usuário encontrado: {usuario['id']}")
        return usuario
    print(f"Erro ao buscar usuário: {response.status_code} - {response.text}")
    return None


def abrir_ticket_movidesk(email_responsavel, descricao):
    responsavel = buscar_usuario_movidesk_por_email(email_responsavel)
    if not responsavel:
        print(f"Responsável não encontrado no Movidesk: {email_responsavel}")
        return

    owner = buscar_usuario_movidesk_por_email(SMTP_USER)
    if not owner:
        print(f"Usuário SMTP não encontrado no Movidesk: {SMTP_USER}")
        return

    agora_iso = datetime.datetime.now().isoformat(timespec='seconds')

    ticket_data = {
        "type": 2,
        "subject": "Aviso de Segurança - Senha Fraca",
        "status": "Resolvido",
        "urgency": "Baixa",
        "owner": {"id": responsavel['id']},
        "ownerTeam": owner.get('businessName', 'Suporte N1'),
        "clients": [{"id": responsavel['id']}],
        "createdBy": {"id": owner['id']},
        "actions": [{
            "type": 2,
            "description": descricao,
            "createdBy": {"id": owner['id']},
            "timeAppointments": [{
                "activity": "Envio de Alerta de Segurança",
                "createdBy": {"id": owner['id']},
                "periodStart": datetime.datetime.now().isoformat(),
                "periodEnd": (datetime.datetime.now() + datetime.timedelta(minutes=15)).isoformat(),
                "date": datetime.datetime.now().isoformat(),
                "workTime": "00:15:00"
            }]
        }]
    }

    headers = {'Content-Type': 'application/json'}
    response = requests.post(MOVIEDESK_URL, json=ticket_data, headers=headers, params={"token": MOVIEDESK_TOKEN})

    if response.status_code in [200, 201]:
        print(f"Ticket criado com sucesso: {response.json().get('id', 'ID não informado')}")
    else:
        print(f"Falha ao criar ticket: {response.status_code} - {response.json()}")


def enviar_email(destinatarios, assunto, texto_plano, html_corpo):
    msg = EmailMessage()
    msg['Subject'] = assunto
    msg['From'] = SMTP_USER
    msg['To'] = ", ".join(destinatarios)
    msg.set_content(texto_plano)
    msg.add_alternative(html_corpo, subtype='html')

    with smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT) as server:
        server.login(SMTP_USER, SMTP_PASSWORD)
        server.send_message(msg)
    print(f"E-mail enviado para: {destinatarios}")

def main():
    conteudo_exemplo1 = """Coloque os emails aqui
    <EMAIL>"""
    emails = extrair_emails(conteudo_exemplo1)
    grupos = agrupar_por_dominio(emails)

    responsaveis = carregar_responsaveis("responsaveis.xlsx")

    for dominio, lista_emails in grupos.items():
        email_responsavel = responsaveis.get(dominio, None)
        destinatarios = lista_emails + ([email_responsavel] if email_responsavel else [])
        nome_cliente = dominio.split('.')[0].capitalize()

        texto_plano = PLAINTEXT_TEMPLATE.format(nome_cliente=nome_cliente, lista_emails='\n'.join(lista_emails))
        html_corpo = HTML_TEMPLATE.format(nome_cliente=nome_cliente, lista_emails_li=''.join(f"<li>{email}</li>" for email in lista_emails))

        enviar_email(destinatarios, "Aviso de Segurança - Senha Fraca", texto_plano, html_corpo)

        if email_responsavel:
            abrir_ticket_movidesk(email_responsavel, html_corpo)

if __name__ == "__main__":
    main()
