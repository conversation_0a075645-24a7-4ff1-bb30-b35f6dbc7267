#!/bin/bash

# Script de instalação automatizada do Tactical RMM para Ubuntu Server
# Instala apenas Tactical RMM Agent e configura IP fixo (opcional)
# Autor: Paulo Matheus
# Data: $(date +%Y-%m-%d)

set -e  # Para o script em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Detectar versão do Ubuntu e arquitetura
UBUNTU_VERSION=""
ARCHITECTURE=$(uname -m)
IS_ARM=false

if grep -q "Ubuntu 24.04" /etc/os-release; then
    UBUNTU_VERSION="24.04"
    log "Ubuntu 24.04 detectado"
elif grep -q "Ubuntu 22.04" /etc/os-release; then
    UBUNTU_VERSION="22.04"
    log "Ubuntu 22.04 detectado"
elif grep -q "Ubuntu 20.04" /etc/os-release; then
    UBUNTU_VERSION="20.04"
    log "Ubuntu 20.04 detectado"
else
    error "Este script foi desenvolvido para Ubuntu 20.04, 22.04 ou 24.04"
fi

# Detectar arquitetura ARM
if [[ "$ARCHITECTURE" == "aarch64" ]] || [[ "$ARCHITECTURE" == "armv7l" ]] || [[ "$ARCHITECTURE" == "arm64" ]]; then
    IS_ARM=true
    log "Arquitetura ARM detectada: $ARCHITECTURE"
else
    log "Arquitetura x86_64 detectada: $ARCHITECTURE"
fi

log "Bem Vindo(a) a instalação automatizada do Tactical RMM para Ubuntu Server 20.04/22.04/24.04 (x86_64/ARM)."

echo "
                               #######
       ####################### #######
     #####                     #######
     ###                        #####
    ###   ######### ####   #########              ##### #### #################  ############# ####             ####        #####          ####
    ###   ########## ####   ###  #####            #####  #### ################## ############# ####             ####       #######         ####
    ###   ########### ####   ###   #####          #####   #### ####         ######    #####     ####             ####      #########        ####
    ###   ############ ####   ###    #####        #####    #### ####           ####    #####     ####             ####     ##### #####       ####
    ###   ############# ####   ###     #####      #####     #### ####           ####    #####     ####             ####    #####   #####      ####
    ###   ############## ####   ###      #####    #####      #### ####       ########    #####     ####             ####   #####     #####     ####
    ###   ############### ####   ###       #####  #####       #### ####      ########     #####     ####             ####  ####        #####    ####
    ###   ################ ####   ###        ##########        #### ####       ######      #####     ######          ##########          #####   ####
    ###   ################# ####   ###         ########         #### ####        #######    #####      ########################            #####  #################
    ###   ################## ####   ###          ######          #### ####          ######   #####       ######################              #######################
    ###   ################### ####   ###            ##            #### ###             #####   ###            #########    ####                ######################
   ######                       ####                                                                                                             #### ###########
   ###### #########################                                                                                                              ############  ##
   ###### ######################
"

log "Software Versão: 1.18.0"
log "Desenvolvido por Paulo Matheus - NVirtual"
log "Iniciando instalação automatizada do Tactical RMM..."

# Solicitar informações do Tactical RMM
echo
log "Configuração do Tactical RMM..."
read -p "Digite o ID do Cliente (ID_CLIENT): " TACTICAL_CLIENT_ID
if [[ -z "$TACTICAL_CLIENT_ID" ]]; then
    error "ID do Cliente é obrigatório"
fi

read -p "Digite a Filial do Cliente (FILIAL_CLIENT): " TACTICAL_CLIENT_FILIAL
if [[ -z "$TACTICAL_CLIENT_FILIAL" ]]; then
    error "Filial do Cliente é obrigatória"
fi

# Perguntar se deseja configurar IP fixo
echo
log "Configuração de rede..."
read -p "Deseja configurar IP fixo? (s/N): " CONFIGURE_STATIC_IP
CONFIGURE_STATIC_IP=${CONFIGURE_STATIC_IP:-N}

if [[ "$CONFIGURE_STATIC_IP" =~ ^[Ss]$ ]]; then
    # Detectar IP atual e calcular IP fixo
    log "Detectando configuração de rede atual..."
    CURRENT_IP=$(ip route get ******* | awk '{print $7; exit}')
    INTERFACE=$(ip route get ******* | awk '{print $5; exit}')
    GATEWAY=$(ip route | grep default | awk '{print $3; exit}')

    if [[ -z "$CURRENT_IP" ]]; then
        error "Não foi possível detectar o IP atual"
    fi

    # Extrair a rede e calcular o IP .222
    IFS='.' read -ra IP_PARTS <<< "$CURRENT_IP"
    NETWORK="${IP_PARTS[0]}.${IP_PARTS[1]}.${IP_PARTS[2]}"
    PROPOSED_IP="${NETWORK}.222"

    info "IP atual detectado: $CURRENT_IP"
    info "Interface de rede: $INTERFACE"
    info "Gateway: $GATEWAY"
    info "IP proposto: $PROPOSED_IP"

    # Verificar se o IP .222 já está em uso
    log "Verificando se o IP $PROPOSED_IP já está em uso..."
    if ping -c 3 -W 2 "$PROPOSED_IP" > /dev/null 2>&1; then
        warning "O IP $PROPOSED_IP já está em uso por outro equipamento!"
        read -p "Digite o IP fixo desejado: " FIXED_IP
        if [[ -z "$FIXED_IP" ]]; then
            error "IP fixo é obrigatório"
        fi

        # Verificar se o IP customizado também está em uso
        log "Verificando se o IP $FIXED_IP está disponível..."
        if ping -c 3 -W 2 "$FIXED_IP" > /dev/null 2>&1; then
            error "O IP $FIXED_IP também já está em uso! Escolha outro IP."
        fi
    else
        log "IP $PROPOSED_IP está disponível!"
        FIXED_IP="$PROPOSED_IP"

        read -p "Confirma a configuração do IP fixo $FIXED_IP? (s/N): " CONFIRM_IP
        if [[ ! "$CONFIRM_IP" =~ ^[Ss]$ ]]; then
            read -p "Digite o IP fixo desejado: " USER_INPUT
            if [[ -n "$USER_INPUT" ]]; then
                FIXED_IP="$USER_INPUT"
                # Verificar se o IP customizado está em uso
                log "Verificando se o IP $FIXED_IP está disponível..."
                if ping -c 3 -W 2 "$FIXED_IP" > /dev/null 2>&1; then
                    error "O IP $FIXED_IP já está em uso! Escolha outro IP."
                fi
            else
                error "IP fixo é obrigatório"
            fi
        fi
    fi

    # Detectar DNS servers
    DNS_SERVERS=$(systemd-resolve --status 2>/dev/null | grep "DNS Servers" | head -1 | awk '{print $3,$4}' | tr ' ' ',' || echo "*******,*******")
    if [[ -z "$DNS_SERVERS" ]] || [[ "$DNS_SERVERS" == "," ]]; then
        DNS_SERVERS="*******,*******"
        warning "Usando DNS padrão: $DNS_SERVERS"
    fi
else
    log "Mantendo configuração DHCP atual"
    CONFIGURE_STATIC_IP="N"
fi

log "Atualizando sistema..."
sudo apt-get update -y
sudo apt-get upgrade -y

log "Instalando pacotes básicos..."
sudo apt-get install curl wget net-tools -y

# Instalar Tactical RMM
log "Instalando Tactical RMM Agent..."

# Função para download com retry e URLs alternativas
download_tactical_script() {
    local script_name="rmmagent-linux.sh"
    local max_attempts=3
    local wait_time=5

    # URLs alternativas para o script
    local urls=(
        "https://raw.githubusercontent.com/netvolt/LinuxRMM-Script/main/rmmagent-linux.sh"
        "https://github.com/netvolt/LinuxRMM-Script/raw/main/rmmagent-linux.sh"
        "https://raw.githubusercontent.com/netvolt/LinuxRMM-Script/master/rmmagent-linux.sh"
    )

    cd /tmp

    # Remover arquivo anterior se existir
    rm -f "$script_name"

    for url in "${urls[@]}"; do
        log "Tentando baixar de: $url"

        for attempt in $(seq 1 $max_attempts); do
            info "Tentativa $attempt de $max_attempts..."

            if wget --timeout=30 --tries=1 "$url" -O "$script_name" 2>/dev/null; then
                if [[ -f "$script_name" ]] && [[ -s "$script_name" ]]; then
                    log "✅ Script baixado com sucesso!"
                    sudo chmod +x "$script_name"
                    return 0
                else
                    warning "Arquivo baixado está vazio ou corrompido"
                    rm -f "$script_name"
                fi
            else
                warning "Falha no download (tentativa $attempt)"
            fi

            if [[ $attempt -lt $max_attempts ]]; then
                info "Aguardando ${wait_time}s antes da próxima tentativa..."
                sleep $wait_time
                wait_time=$((wait_time + 5))  # Aumentar tempo de espera
            fi
        done

        warning "Falha em todas as tentativas para URL: $url"
    done

    error "Não foi possível baixar o script do Tactical RMM de nenhuma URL. Verifique sua conectividade."
}

# Baixar script do Tactical RMM com retry
download_tactical_script

info "Instalando Tactical RMM Agent com as configurações fornecidas..."
info "Cliente ID: $TACTICAL_CLIENT_ID"
info "Filial: $TACTICAL_CLIENT_FILIAL"

# Executar instalação do Tactical RMM com os parâmetros corretos baseado na arquitetura
if [[ "$IS_ARM" == "true" ]]; then
    # URL para ARM/Raspberry Pi
    TACTICAL_MESH_URL='https://mesh.centralmesh.nvirtual.com.br/meshagents?id=7Nss2LHe67mTwByGHQ3H3lOI4x8Awfk6kwbQgxSMMq%40qIJKjK6OOSBMWfXBYgPlb&installflags=0&meshinstall=26'
    log "Instalando Tactical RMM para arquitetura ARM..."
else
    # URL para x86_64
    TACTICAL_MESH_URL='https://mesh.centralmesh.nvirtual.com.br/meshagents?id=7Nss2LHe67mTwByGHQ3H3lOI4x8Awfk6kwbQgxSMMq%40qIJKjK6OOSBMWfXBYgPlb&installflags=2&meshinstall=6'
    log "Instalando Tactical RMM para arquitetura x86_64..."
fi

./rmmagent-linux.sh install "$TACTICAL_MESH_URL" 'https://api.centralmesh.nvirtual.com.br' "$TACTICAL_CLIENT_ID" "$TACTICAL_CLIENT_FILIAL" 'ecd275ac5baa7e615674a38f2de333f00dd2635e179f9a08e4026db2e5856ae3' 'server'

if [[ $? -eq 0 ]]; then
    log "Tactical RMM Agent instalado com sucesso!"
else
    warning "Houve um problema na instalação do Tactical RMM Agent. Verifique os logs."
fi

# Configuração de firewall básico
log "Configuração de firewall básico..."

# Verificar se ufw está instalado, se não, instalar
if ! command -v ufw &> /dev/null; then
    log "UFW não encontrado. Instalando..."
    sudo apt-get update -y
    sudo apt-get install ufw -y
    
    # Verificar se a instalação foi bem-sucedida
    if ! command -v ufw &> /dev/null; then
        warning "Não foi possível instalar o UFW. Configurando firewall com iptables..."
        
        # Configuração básica com iptables como fallback
        sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
        sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
        sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
        sudo iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
        sudo iptables -A INPUT -i lo -j ACCEPT
        
        # Salvar regras do iptables
        if command -v iptables-save &> /dev/null; then
            sudo iptables-save > /etc/iptables/rules.v4 2>/dev/null || true
        fi
        
        log "Firewall configurado com iptables"
    else
        log "UFW instalado com sucesso. Configurando..."
        sudo ufw allow 22/tcp
        sudo ufw allow 80/tcp
        sudo ufw allow 443/tcp
        sudo ufw --force enable
        log "Firewall UFW configurado"
    fi
else
    log "UFW já está instalado. Configurando..."
    sudo ufw allow 22/tcp
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    sudo ufw --force enable
    log "Firewall UFW configurado"
fi

# Detectar se está sendo executado via SSH
SSH_CONNECTION_DETECTED=false
if [[ -n "$SSH_CLIENT" ]] || [[ -n "$SSH_TTY" ]] || [[ "$TERM" == "xterm"* ]] && pstree -p $$ | grep -q ssh; then
    SSH_CONNECTION_DETECTED=true
    SSH_CLIENT_IP=$(echo $SSH_CLIENT | awk '{print $1}')
    log "⚠️  Conexão SSH detectada de: $SSH_CLIENT_IP"
fi

# Configurar IP fixo se solicitado
if [[ "$CONFIGURE_STATIC_IP" =~ ^[Ss]$ ]]; then
    log "Configurando IP fixo..."
    NETPLAN_FILE="/etc/netplan/01-netcfg.yaml"

    # Backup da configuração atual
    sudo cp $NETPLAN_FILE ${NETPLAN_FILE}.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

    # Formatar DNS servers corretamente para netplan
    DNS_ARRAY=$(echo $DNS_SERVERS | sed 's/,/, /g')

    # Aviso especial para conexões SSH
    if [[ "$SSH_CONNECTION_DETECTED" == "true" ]]; then
        echo
        warning "🚨 ATENÇÃO: Conexão SSH detectada!"
        warning "A configuração de IP fixo pode interromper sua conexão SSH atual."
        echo
        info "📋 Informações importantes:"
        info "• IP atual (DHCP): $CURRENT_IP"
        info "• IP fixo que será configurado: $FIXED_IP"
        info "• Interface de rede: $INTERFACE"
        info "• Gateway: $GATEWAY"
        echo
        info "🔧 Após a aplicação da configuração:"
        info "• Se a conexão SSH for perdida, reconecte usando: ssh user@$FIXED_IP"
        info "• Se houver problemas, acesse fisicamente o servidor e execute:"
        info "  sudo cp ${NETPLAN_FILE}.backup.* $NETPLAN_FILE && sudo netplan apply"
        echo

        read -p "⚠️  Deseja continuar com a configuração de IP fixo? (s/N): " CONFIRM_SSH_RISK
        if [[ ! "$CONFIRM_SSH_RISK" =~ ^[Ss]$ ]]; then
            log "Configuração de IP fixo cancelada pelo usuário."
            log "Mantendo configuração DHCP atual: $CURRENT_IP"
            CONFIGURE_STATIC_IP="N"
            FINAL_IP="$CURRENT_IP (DHCP - Mantido por segurança SSH)"
        else
            log "Usuário confirmou configuração de IP fixo via SSH."
        fi
    fi

    # Aplicar configuração apenas se não foi cancelada
    if [[ "$CONFIGURE_STATIC_IP" =~ ^[Ss]$ ]]; then
        # Criar nova configuração netplan
        sudo tee $NETPLAN_FILE > /dev/null <<EOF
network:
  version: 2
  renderer: networkd
  ethernets:
    $INTERFACE:
      dhcp4: false
      addresses:
        - $FIXED_IP/24
      routes:
        - to: default
          via: $GATEWAY
      nameservers:
        addresses: [$DNS_ARRAY]
EOF

        if [[ "$SSH_CONNECTION_DETECTED" == "true" ]]; then
            echo
            warning "🔄 Aplicando configuração de rede em 5 segundos..."
            warning "⚠️  A conexão SSH pode ser interrompida momentaneamente."
            info "🔗 Reconecte usando: ssh user@$FIXED_IP"
            echo
            for i in {5..1}; do
                echo -n "⏳ $i... "
                sleep 1
            done
            echo
        fi

        log "Aplicando configuração de rede..."

        # Para conexões SSH, aplicar em background para evitar travamento
        if [[ "$SSH_CONNECTION_DETECTED" == "true" ]]; then
            # Aplicar netplan em background e aguardar
            (sudo netplan apply && sleep 2) &
            NETPLAN_PID=$!

            # Aguardar aplicação com timeout
            sleep 8

            # Verificar se o processo ainda está rodando
            if kill -0 $NETPLAN_PID 2>/dev/null; then
                log "Configuração de rede aplicada em background."
            fi
        else
            # Aplicação normal para conexões locais
            sudo netplan apply
            sleep 5
        fi

        # Verificar conectividade
        if ! ping -c 3 ******* > /dev/null 2>&1; then
            warning "Possível problema de conectividade após configurar IP fixo."
            if [[ "$SSH_CONNECTION_DETECTED" == "true" ]]; then
                warning "Se você perdeu a conexão SSH, tente reconectar em: ssh user@$FIXED_IP"
            fi
            info "Para restaurar: sudo cp ${NETPLAN_FILE}.backup.* $NETPLAN_FILE && sudo netplan apply"
        else
            log "IP fixo configurado com sucesso: $FIXED_IP"
            if [[ "$SSH_CONNECTION_DETECTED" == "true" ]]; then
                info "✅ Conexão SSH mantida. Novo IP: $FIXED_IP"
            fi
        fi

        FINAL_IP="$FIXED_IP (Fixo)"
    fi
else
    log "Mantendo configuração DHCP atual..."
    CURRENT_IP=$(ip route get ******* | awk '{print $7; exit}')
    FINAL_IP="$CURRENT_IP (DHCP)"
fi

log "Instalação do Tactical RMM concluída com sucesso!"
echo
echo "=========================================="
echo "RESUMO DA INSTALAÇÃO - TACTICAL RMM"
echo "=========================================="
echo "Configuração de IP: $FINAL_IP"
if [[ "$CONFIGURE_STATIC_IP" =~ ^[Ss]$ ]]; then
    echo "Interface de rede: $INTERFACE"
    echo "Gateway: $GATEWAY"
    echo "DNS: $DNS_SERVERS"
fi
echo
echo "Tactical RMM:"
echo "- Cliente ID: $TACTICAL_CLIENT_ID"
echo "- Filial: $TACTICAL_CLIENT_FILIAL"
echo "- Mesh Server: mesh.centralmesh.nvirtual.com.br"
echo "- API Server: api.centralmesh.nvirtual.com.br"
echo "- Arquitetura: $ARCHITECTURE"
echo
echo "Serviços instalados e ativos:"
echo "- Tactical RMM Agent"
echo "- MeshAgent"
echo
echo "Portas liberadas no firewall:"
echo "- SSH (porta 22)"
echo "- HTTP (porta 80)"
echo "- HTTPS (porta 443)"
echo
echo "Para verificar status dos serviços:"
echo "sudo systemctl status tacticalagent"
echo "sudo systemctl status meshagent"

# Mensagem especial para conexões SSH
if [[ "$SSH_CONNECTION_DETECTED" == "true" ]]; then
    echo
    echo "🔗 INFORMAÇÕES IMPORTANTES PARA SSH:"
    echo "=========================================="
    if [[ "$FINAL_IP" == *"Fixo"* ]]; then
        echo "✅ IP fixo configurado com sucesso!"
        echo "🔄 Para futuras conexões SSH, use:"
        echo "   ssh $(whoami)@$FIXED_IP"
        echo
        echo "⚠️  Se você perdeu a conexão SSH:"
        echo "   1. Aguarde 30 segundos para estabilização"
        echo "   2. Reconecte usando: ssh $(whoami)@$FIXED_IP"
        echo "   3. Se não conseguir conectar, acesse fisicamente"
        echo "      e execute: sudo cp ${NETPLAN_FILE}.backup.* $NETPLAN_FILE && sudo netplan apply"
    else
        echo "✅ Configuração DHCP mantida por segurança SSH"
        echo "🔗 Continue usando o IP atual: $CURRENT_IP"
    fi
    echo "=========================================="
fi

echo
echo "Desenvolvido por: Paulo Matheus"
echo "=========================================="

log "Script finalizado!"
