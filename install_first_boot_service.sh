#!/bin/bash

# Script para configurar o serviço de primeiro boot no Raspberry Pi
# Este script deve ser executado ANTES de criar a imagem do SD card
# Autor: Paulo Matheus
# Data: $(date +%Y-%m-%d)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

log "=== CONFIGURADOR DE PRIMEIRO BOOT PARA RASPBERRY PI ==="
log "Configurando serviço systemd para execução automática..."

# Verificar se está rodando como root
if [[ $EUID -ne 0 ]]; then
   error "Este script deve ser executado como root (sudo)"
fi

# Verificar se o script setup_first_boot.sh existe
if [[ ! -f "setup_first_boot.sh" ]]; then
    error "Arquivo setup_first_boot.sh não encontrado no diretório atual"
fi

# Copiar o script para o local apropriado
log "Copiando script de primeiro boot..."
cp setup_first_boot.sh /usr/local/bin/
chmod +x /usr/local/bin/setup_first_boot.sh

# Criar o arquivo de serviço systemd
log "Criando serviço systemd..."
cat > /etc/systemd/system/first-boot-setup.service << 'EOF'
[Unit]
Description=First Boot Setup - Zabbix and Tactical RMM Installation
After=network-online.target
Wants=network-online.target
DefaultDependencies=false

[Service]
Type=oneshot
ExecStart=/usr/local/bin/setup_first_boot.sh
StandardOutput=journal
StandardError=journal
TimeoutStartSec=1800
RemainAfterExit=yes
User=root

[Install]
WantedBy=multi-user.target
EOF

# Habilitar o serviço
log "Habilitando serviço para execução no boot..."
systemctl daemon-reload
systemctl enable first-boot-setup.service

# Criar script de informações para o usuário
log "Criando arquivo de instruções..."
cat > /home/<USER>/INSTRUCOES_PRIMEIRO_BOOT.txt << 'EOF'
=== RASPBERRY PI PLUG AND PLAY - INSTRUÇÕES ===

Este Raspberry Pi foi configurado para instalação automática no primeiro boot.

=== O QUE ACONTECERÁ NO PRIMEIRO BOOT ===
1. O sistema aguardará conectividade de rede
2. Baixará automaticamente o script de instalação do GitHub
3. Instalará e configurará:
   - Zabbix Proxy (hostname: "MUDAR")
   - Zabbix Agent
   - Tactical RMM Agent (Client ID: 1, Site ID: 1)
4. Configurará firewall básico
5. Criará arquivo com informações do sistema

=== CONFIGURAÇÕES PADRÃO ===
- Zabbix Hostname: MUDAR
- Tactical Client ID: 1
- Tactical Site ID: 1
- Zabbix Server: monitora.nvirtual.com.br
- Tactical Mesh: mesh.centralmesh.nvirtual.com.br

=== APÓS O PRIMEIRO BOOT ===
1. Verifique o arquivo: /home/<USER>/SISTEMA_INFO.txt
2. Acesse o Zabbix Server e altere o nome do proxy
3. O sistema já estará monitorado pelo Tactical RMM

=== REQUISITOS ===
- Conexão com a internet no primeiro boot
- Aguardar aproximadamente 10-15 minutos para conclusão

=== LOGS ===
Para acompanhar o progresso:
sudo journalctl -u first-boot-setup.service -f

=== DESENVOLVIDO POR ===
Paulo Matheus - NVirtual
EOF

chown pi:pi /home/<USER>/INSTRUCOES_PRIMEIRO_BOOT.txt

# Criar script para verificar status da instalação
log "Criando script de verificação..."
cat > /usr/local/bin/check_first_boot_status.sh << 'EOF'
#!/bin/bash

# Script para verificar o status da instalação do primeiro boot

GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo "=== STATUS DA INSTALAÇÃO DO PRIMEIRO BOOT ==="
echo

# Verificar se o serviço foi executado
if systemctl is-enabled first-boot-setup.service > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Serviço de primeiro boot está habilitado${NC}"
    
    if [[ -f "/home/<USER>/.first_boot_completed" ]]; then
        echo -e "${GREEN}✅ Instalação do primeiro boot foi concluída${NC}"
        
        # Verificar serviços
        echo
        echo "=== STATUS DOS SERVIÇOS ==="
        
        if systemctl is-active --quiet zabbix-proxy; then
            echo -e "${GREEN}✅ Zabbix Proxy está ativo${NC}"
        else
            echo -e "${RED}❌ Zabbix Proxy não está ativo${NC}"
        fi
        
        if systemctl is-active --quiet zabbix-agent; then
            echo -e "${GREEN}✅ Zabbix Agent está ativo${NC}"
        else
            echo -e "${RED}❌ Zabbix Agent não está ativo${NC}"
        fi
        
        echo
        echo "=== INFORMAÇÕES DO SISTEMA ==="
        if [[ -f "/home/<USER>/SISTEMA_INFO.txt" ]]; then
            echo -e "${GREEN}📄 Arquivo de informações disponível em: /home/<USER>/SISTEMA_INFO.txt${NC}"
        fi
        
    else
        echo -e "${YELLOW}⏳ Instalação do primeiro boot ainda não foi executada${NC}"
        echo "Execute: sudo systemctl start first-boot-setup.service"
    fi
else
    echo -e "${RED}❌ Serviço de primeiro boot não está configurado${NC}"
fi

echo
echo "=== COMANDOS ÚTEIS ==="
echo "Ver logs da instalação: sudo journalctl -u first-boot-setup.service"
echo "Executar manualmente: sudo systemctl start first-boot-setup.service"
echo "Ver informações do sistema: cat /home/<USER>/SISTEMA_INFO.txt"
EOF

chmod +x /usr/local/bin/check_first_boot_status.sh

# Criar alias para facilitar o uso
echo "alias check-setup='sudo /usr/local/bin/check_first_boot_status.sh'" >> /home/<USER>/.bashrc

log "✅ Configuração concluída com sucesso!"
echo
echo "=========================================="
echo "CONFIGURAÇÃO DE PRIMEIRO BOOT INSTALADA"
echo "=========================================="
echo
info "📋 O que foi configurado:"
info "• Serviço systemd: first-boot-setup.service"
info "• Script principal: /usr/local/bin/setup_first_boot.sh"
info "• Script de verificação: /usr/local/bin/check_first_boot_status.sh"
info "• Instruções: /home/<USER>/INSTRUCOES_PRIMEIRO_BOOT.txt"
echo
info "🔧 Comandos úteis:"
info "• Verificar status: check-setup"
info "• Ver logs: sudo journalctl -u first-boot-setup.service -f"
info "• Executar manualmente: sudo systemctl start first-boot-setup.service"
echo
warning "⚠️  IMPORTANTE:"
warning "• Este sistema executará a instalação automaticamente no próximo boot"
warning "• Certifique-se de que haverá conexão com a internet"
warning "• A instalação pode levar 10-15 minutos"
echo
log "🎯 Sistema pronto para ser usado como imagem plug-and-play!"
