#!/bin/bash

# Script para configurar o serviço de primeiro boot com exibição visual
# Este script deve ser executado ANTES de criar a imagem do SD card
# Autor: Paulo Matheus
# Data: $(date +%Y-%m-%d)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

log "=== CONFIGURADOR DE PRIMEIRO BOOT VISUAL PARA RASPBERRY PI ==="
log "Configurando serviço systemd com exibição na tela..."

# Verificar se está rodando como root
if [[ $EUID -ne 0 ]]; then
   error "Este script deve ser executado como root (sudo)"
fi

# Verificar se o script setup_first_boot_visual.sh existe
if [[ ! -f "setup_first_boot_visual.sh" ]]; then
    error "Arquivo setup_first_boot_visual.sh não encontrado no diretório atual"
fi

# Copiar o script para o local apropriado
log "Copiando script de primeiro boot visual..."
cp setup_first_boot_visual.sh /usr/local/bin/
chmod +x /usr/local/bin/setup_first_boot_visual.sh

# Criar script wrapper que redireciona para tty1
log "Criando script wrapper para exibição visual..."
cat > /usr/local/bin/first_boot_wrapper.sh << 'EOF'
#!/bin/bash

# Wrapper para exibir instalação na tela principal
# Redireciona saída para tty1 (console principal)

# Configurar tty1 para exibir cores
export TERM=linux

# Executar script principal
/usr/local/bin/setup_first_boot_visual.sh 2>&1 | tee /dev/tty1

# Manter tela visível por alguns segundos
sleep 5
EOF

chmod +x /usr/local/bin/first_boot_wrapper.sh

# Criar o arquivo de serviço systemd com exibição visual
log "Criando serviço systemd com exibição visual..."
cat > /etc/systemd/system/first-boot-setup-visual.service << 'EOF'
[Unit]
Description=First Boot Setup Visual - Zabbix and Tactical RMM Installation
After=network-online.target <EMAIL>
Wants=network-online.target
DefaultDependencies=false

[Service]
Type=oneshot
ExecStart=/usr/local/bin/first_boot_wrapper.sh
StandardOutput=tty
StandardError=tty
TTYPath=/dev/tty1
TimeoutStartSec=1800
RemainAfterExit=yes
User=root
Environment=TERM=linux

[Install]
WantedBy=multi-user.target
EOF

# Habilitar o serviço
log "Habilitando serviço para execução no boot..."
systemctl daemon-reload
systemctl enable first-boot-setup-visual.service

# Desabilitar serviço getty@tty1 temporariamente para evitar conflito
log "Configurando console principal..."
<NAME_EMAIL> 2>/dev/null || true

# Criar script para reabilitar getty após instalação
cat > /usr/local/bin/restore_getty.sh << 'EOF'
#!/bin/bash
# Script para restaurar getty@tty1 após instalação
<NAME_EMAIL>
<NAME_EMAIL>
EOF

chmod +x /usr/local/bin/restore_getty.sh

# Modificar o script principal para restaurar getty no final
sed -i '/reboot$/i /usr/local/bin/restore_getty.sh' /usr/local/bin/setup_first_boot_visual.sh

# Criar arquivo de configuração para auto-login no tty1 durante instalação
log "Configurando auto-login temporário..."
mkdir -p /etc/systemd/system/<EMAIL>.d/
cat > /etc/systemd/system/<EMAIL>.d/override.conf << 'EOF'
[Service]
ExecStart=
ExecStart=-/sbin/agetty --autologin pi --noclear %I $TERM
EOF

# Criar script de informações para o usuário
log "Criando arquivo de instruções..."
cat > /home/<USER>/INSTRUCOES_PRIMEIRO_BOOT_VISUAL.txt << 'EOF'
=== RASPBERRY PI PLUG AND PLAY - INSTRUÇÕES (VERSÃO VISUAL) ===

Este Raspberry Pi foi configurado para instalação automática VISUAL no primeiro boot.

=== O QUE ACONTECERÁ NO PRIMEIRO BOOT ===
🖥️  A instalação será exibida na tela principal (tty1)
📊 Você verá barras de progresso e status em tempo real
⏱️  O processo completo leva aproximadamente 10-15 minutos

=== ETAPAS VISUAIS ===
1. 🌐 Verificação de conectividade de rede
2. 📥 Download do script de instalação
3. ⚙️  Configuração automática
4. 🔧 Instalação do Zabbix Proxy + Agent
5. 📡 Instalação do Tactical RMM
6. 🔥 Configuração de firewall
7. 🌍 Configuração de rede
8. ✅ Finalização e reinicialização

=== CONFIGURAÇÕES PADRÃO ===
- Zabbix Hostname: MUDAR
- Tactical Client ID: 1
- Tactical Site ID: 1
- Zabbix Server: monitora.nvirtual.com.br

=== APÓS A INSTALAÇÃO ===
1. ✅ Tela de sucesso será exibida
2. 📄 Arquivo de informações criado: /home/<USER>/SISTEMA_INFO.txt
3. 🔄 Sistema reiniciará automaticamente
4. 🌐 Acesse Zabbix e altere nome do proxy

=== ACOMPANHAR INSTALAÇÃO ===
- A instalação é exibida automaticamente na tela
- Barras de progresso mostram o andamento
- Status detalhado de cada etapa
- Mensagens coloridas para facilitar leitura

=== LOGS ALTERNATIVOS ===
Se precisar ver logs detalhados:
sudo journalctl -u first-boot-setup-visual.service -f

=== DESENVOLVIDO POR ===
Paulo Matheus - NVirtual
EOF

chown pi:pi /home/<USER>/INSTRUCOES_PRIMEIRO_BOOT_VISUAL.txt

# Criar script de verificação visual
log "Criando script de verificação visual..."
cat > /usr/local/bin/check_visual_boot_status.sh << 'EOF'
#!/bin/bash

# Script para verificar o status da instalação visual

GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

clear
echo -e "${BLUE}
╔══════════════════════════════════════════════════════════════╗
║              STATUS DA INSTALAÇÃO VISUAL                    ║
╚══════════════════════════════════════════════════════════════╝${NC}"
echo

# Verificar se o serviço foi executado
if systemctl is-enabled first-boot-setup-visual.service > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Serviço de primeiro boot visual está habilitado${NC}"
    
    if [[ -f "/var/lib/first_boot_completed" ]]; then
        echo -e "${GREEN}✅ Instalação visual do primeiro boot foi concluída${NC}"
        
        # Verificar serviços
        echo
        echo -e "${BLUE}=== STATUS DOS SERVIÇOS ===${NC}"
        
        if systemctl is-active --quiet zabbix-proxy; then
            echo -e "${GREEN}✅ Zabbix Proxy está ativo${NC}"
        else
            echo -e "${RED}❌ Zabbix Proxy não está ativo${NC}"
        fi
        
        if systemctl is-active --quiet zabbix-agent; then
            echo -e "${GREEN}✅ Zabbix Agent está ativo${NC}"
        else
            echo -e "${RED}❌ Zabbix Agent não está ativo${NC}"
        fi
        
        echo
        echo -e "${BLUE}=== INFORMAÇÕES DO SISTEMA ===${NC}"
        # Procurar arquivo de informações em locais possíveis
        INFO_FILE=""
        for dir in "/home/<USER>" "/home/<USER>" "/home/<USER>" "/root"; do
            if [[ -f "$dir/SISTEMA_INFO.txt" ]]; then
                INFO_FILE="$dir/SISTEMA_INFO.txt"
                break
            fi
        done

        if [[ -n "$INFO_FILE" ]]; then
            echo -e "${GREEN}📄 Arquivo de informações: $INFO_FILE${NC}"
            echo -e "${BLUE}IP do sistema: $(hostname -I | awk '{print $1}')${NC}"
        else
            echo -e "${YELLOW}⚠️ Arquivo de informações não encontrado${NC}"
        fi
        
    else
        echo -e "${YELLOW}⏳ Instalação visual ainda não foi executada${NC}"
        echo "Execute: sudo systemctl start first-boot-setup-visual.service"
    fi
else
    echo -e "${RED}❌ Serviço de primeiro boot visual não está configurado${NC}"
fi

echo
echo -e "${BLUE}=== COMANDOS ÚTEIS ===${NC}"
echo "Ver logs da instalação: sudo journalctl -u first-boot-setup-visual.service"
echo "Executar manualmente: sudo systemctl start first-boot-setup-visual.service"
echo "Ver informações: cat /home/<USER>/SISTEMA_INFO.txt"
EOF

chmod +x /usr/local/bin/check_visual_boot_status.sh

# Criar alias para facilitar o uso
echo "alias check-visual='sudo /usr/local/bin/check_visual_boot_status.sh'" >> /home/<USER>/.bashrc

log "✅ Configuração visual concluída com sucesso!"
echo
echo "=========================================="
echo "CONFIGURAÇÃO VISUAL DE PRIMEIRO BOOT INSTALADA"
echo "=========================================="
echo
info "📋 O que foi configurado:"
info "• Serviço systemd: first-boot-setup-visual.service"
info "• Script principal: /usr/local/bin/setup_first_boot_visual.sh"
info "• Script wrapper: /usr/local/bin/first_boot_wrapper.sh"
info "• Script de verificação: /usr/local/bin/check_visual_boot_status.sh"
info "• Instruções: /home/<USER>/INSTRUCOES_PRIMEIRO_BOOT_VISUAL.txt"
echo
info "🖥️  CARACTERÍSTICAS VISUAIS:"
info "• Exibição na tela principal (tty1)"
info "• Barras de progresso coloridas"
info "• Status detalhado de cada etapa"
info "• Tela de finalização com resumo"
echo
info "🔧 Comandos úteis:"
info "• Verificar status: check-visual"
info "• Ver logs: sudo journalctl -u first-boot-setup-visual.service -f"
echo
warning "⚠️  IMPORTANTE:"
warning "• A instalação será exibida automaticamente na tela"
warning "• Não interfira durante o processo (10-15 minutos)"
warning "• Certifique-se de ter conexão com internet"
echo
log "🎯 Sistema pronto para imagem plug-and-play VISUAL!"
