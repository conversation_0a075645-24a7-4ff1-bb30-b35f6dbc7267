#!/bin/bash

# Script de validação - Verifica se todos os componentes estão prontos
# Execute antes de criar a imagem final
# Autor: <PERSON> Matheus
# Data: $(date +%Y-%m-%d)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[✅] $1${NC}"
}

error() {
    echo -e "${RED}[❌] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[⚠️ ] $1${NC}"
}

info() {
    echo -e "${BLUE}[ℹ️ ] $1${NC}"
}

VALIDATION_PASSED=true

echo "=========================================="
echo "VALIDAÇÃO DA CONFIGURAÇÃO PLUG AND PLAY"
echo "=========================================="
echo
info "Verificando se todos os componentes estão prontos..."
echo

# Verificar arquivos necessários
echo "📁 VERIFICANDO ARQUIVOS NECESSÁRIOS..."
REQUIRED_FILES=(
    "setup_first_boot.sh"
    "install_first_boot_service.sh"
    "prepare_raspberry_image.sh"
    "README_RASPBERRY_AUTOMATION.md"
    "demo_usage.sh"
    "validate_setup.sh"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [[ -f "$file" ]]; then
        log "Arquivo encontrado: $file"
    else
        error "Arquivo faltando: $file"
        VALIDATION_PASSED=false
    fi
done

echo

# Verificar permissões dos scripts
echo "🔐 VERIFICANDO PERMISSÕES..."
SCRIPT_FILES=(
    "setup_first_boot.sh"
    "install_first_boot_service.sh"
    "prepare_raspberry_image.sh"
    "demo_usage.sh"
    "validate_setup.sh"
)

for script in "${SCRIPT_FILES[@]}"; do
    if [[ -f "$script" ]]; then
        if [[ -x "$script" ]]; then
            log "Permissão OK: $script"
        else
            warning "Sem permissão de execução: $script"
            info "Execute: chmod +x $script"
        fi
    fi
done

echo

# Verificar sintaxe dos scripts
echo "🔍 VERIFICANDO SINTAXE DOS SCRIPTS..."
for script in "${SCRIPT_FILES[@]}"; do
    if [[ -f "$script" ]]; then
        if bash -n "$script" 2>/dev/null; then
            log "Sintaxe OK: $script"
        else
            error "Erro de sintaxe: $script"
            VALIDATION_PASSED=false
        fi
    fi
done

echo

# Verificar configurações no script principal
echo "⚙️  VERIFICANDO CONFIGURAÇÕES..."

if [[ -f "setup_first_boot.sh" ]]; then
    # Verificar se as configurações padrão estão definidas
    if grep -q 'ZABBIX_HOSTNAME="MUDAR"' setup_first_boot.sh; then
        log "Hostname Zabbix configurado: MUDAR"
    else
        warning "Hostname Zabbix pode não estar configurado corretamente"
    fi

    if grep -q 'TACTICAL_CLIENT_ID="1"' setup_first_boot.sh; then
        log "Tactical Client ID configurado: 1"
    else
        warning "Tactical Client ID pode não estar configurado corretamente"
    fi

    if grep -q 'TACTICAL_CLIENT_FILIAL="1"' setup_first_boot.sh; then
        log "Tactical Site ID configurado: 1"
    else
        warning "Tactical Site ID pode não estar configurado corretamente"
    fi

    # Verificar token do GitHub
    if grep -q "ghp_" setup_first_boot.sh; then
        log "Token do GitHub encontrado"
    else
        error "Token do GitHub não encontrado ou inválido"
        VALIDATION_PASSED=false
    fi

    # Verificar URL do GitHub
    if grep -q "github.com/nubium-cloud/Zabbix-Proxy-Instalation" setup_first_boot.sh; then
        log "URL do repositório GitHub configurada"
    else
        warning "URL do repositório GitHub pode estar incorreta"
    fi
fi

echo

# Verificar se está em um ambiente adequado
echo "🖥️  VERIFICANDO AMBIENTE..."

# Verificar se é Linux
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    log "Sistema operacional: Linux"
else
    warning "Sistema não é Linux. Scripts desenvolvidos para Linux/Raspberry Pi OS"
fi

# Verificar se systemd está disponível
if command -v systemctl &> /dev/null; then
    log "Systemd disponível"
else
    error "Systemd não encontrado. Necessário para serviços automáticos"
    VALIDATION_PASSED=false
fi

# Verificar conectividade com GitHub
echo
echo "🌐 VERIFICANDO CONECTIVIDADE..."
if ping -c 3 github.com > /dev/null 2>&1; then
    log "Conectividade com GitHub: OK"
else
    warning "Sem conectividade com GitHub. Necessária para download do script"
fi

# Verificar conectividade com servidores NVirtual
if ping -c 3 monitora.nvirtual.com.br > /dev/null 2>&1; then
    log "Conectividade com Zabbix Server: OK"
else
    warning "Sem conectividade com monitora.nvirtual.com.br"
fi

echo

# Verificar espaço em disco
echo "💾 VERIFICANDO ESPAÇO EM DISCO..."
AVAILABLE_SPACE=$(df / | awk 'NR==2 {print $4}')
REQUIRED_SPACE=1048576  # 1GB em KB

if [[ $AVAILABLE_SPACE -gt $REQUIRED_SPACE ]]; then
    log "Espaço em disco suficiente: $(($AVAILABLE_SPACE / 1024))MB disponível"
else
    warning "Pouco espaço em disco: $(($AVAILABLE_SPACE / 1024))MB disponível (recomendado: >1GB)"
fi

echo

# Verificar se é Raspberry Pi (se aplicável)
echo "🍓 VERIFICANDO RASPBERRY PI..."
if [[ -f "/proc/cpuinfo" ]] && grep -q "Raspberry Pi" /proc/cpuinfo; then
    log "Raspberry Pi detectado"
    PI_MODEL=$(grep "Model" /proc/cpuinfo | cut -d: -f2 | xargs)
    info "Modelo: $PI_MODEL"
else
    info "Não é um Raspberry Pi (OK para desenvolvimento)"
fi

echo

# Resumo da validação
echo "=========================================="
echo "RESUMO DA VALIDAÇÃO"
echo "=========================================="
echo

if [[ "$VALIDATION_PASSED" == "true" ]]; then
    log "🎉 VALIDAÇÃO PASSOU! Sistema pronto para criar imagem."
    echo
    info "Próximos passos:"
    info "1. Execute: sudo ./prepare_raspberry_image.sh"
    info "2. Desligue o sistema: sudo shutdown -h now"
    info "3. Clone o SD card para criar a imagem"
    info "4. Use a imagem para deployments plug-and-play"
else
    error "❌ VALIDAÇÃO FALHOU! Corrija os problemas antes de continuar."
    echo
    warning "Problemas encontrados que precisam ser corrigidos:"
    warning "• Verifique arquivos faltando"
    warning "• Corrija erros de sintaxe"
    warning "• Configure token do GitHub"
    warning "• Verifique conectividade"
fi

echo
echo "Para mais informações, consulte: README_RASPBERRY_AUTOMATION.md"
echo "Para ver exemplo de uso: ./demo_usage.sh"
