#!/bin/bash

# Script de configuração para primeiro boot com exibição visual
# Instala automaticamente Zabbix Proxy e Tactical RMM
# Autor: <PERSON> Matheus
# Data: $(date +%Y-%m-%d)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Função para limpar tela e mostrar header
show_header() {
    clear > /dev/tty1 2>/dev/null || true
    local header="
╔══════════════════════════════════════════════════════════════╗
║                RASPBERRY PI PLUG AND PLAY                   ║
║            Instalação Automática - NVirtual                 ║
║                                                              ║
║  🔧 Zabbix Proxy + Agent                                    ║
║  📡 Tactical RMM Agent                                      ║
║  🔥 Firewall + Configuração de Rede                        ║
║                                                              ║
║  Desenvolvido por: Paulo Matheus                            ║
╚══════════════════════════════════════════════════════════════╝
"
    echo -e "${GREEN}$header${NC}" > /dev/tty1 2>/dev/null || true
    echo -e "${GREEN}$header${NC}"
}

# Função para exibir progresso visual
show_progress() {
    local current=$1
    local total=$2
    local description="$3"
    local percent=$((current * 100 / total))
    local bar_length=50
    local filled_length=$((percent * bar_length / 100))
    
    local bar=""
    for ((i=0; i<filled_length; i++)); do bar+="█"; done
    for ((i=filled_length; i<bar_length; i++)); do bar+="░"; done
    
    local progress_msg="
┌─────────────────────────────────────────────────────────────┐
│ PROGRESSO DA INSTALAÇÃO                                     │
├─────────────────────────────────────────────────────────────┤
│ [$bar] $percent%    │
│                                                             │
│ 📋 $description
│                                                             │
└─────────────────────────────────────────────────────────────┘"
    
    echo -e "${CYAN}$progress_msg${NC}" > /dev/tty1 2>/dev/null || true
    echo -e "${CYAN}$progress_msg${NC}"
}

# Função para log com exibição na tela
log() {
    local message="✅ [$(date '+%H:%M:%S')] $1"
    echo -e "${GREEN}$message${NC}"
    echo -e "${GREEN}$message${NC}" > /dev/tty1 2>/dev/null || true
}

error() {
    local message="❌ [ERRO] $1"
    echo -e "${RED}$message${NC}"
    echo -e "${RED}$message${NC}" > /dev/tty1 2>/dev/null || true
    exit 1
}

warning() {
    local message="⚠️  [AVISO] $1"
    echo -e "${YELLOW}$message${NC}"
    echo -e "${YELLOW}$message${NC}" > /dev/tty1 2>/dev/null || true
}

info() {
    local message="ℹ️  [INFO] $1"
    echo -e "${BLUE}$message${NC}"
    echo -e "${BLUE}$message${NC}" > /dev/tty1 2>/dev/null || true
}

# Função para mostrar status atual
show_status() {
    local status="$1"
    local details="$2"
    local status_msg="
┌─────────────────────────────────────────────────────────────┐
│ STATUS ATUAL: $status
│ $details
└─────────────────────────────────────────────────────────────┘"
    
    echo -e "${MAGENTA}$status_msg${NC}" > /dev/tty1 2>/dev/null || true
    echo -e "${MAGENTA}$status_msg${NC}"
}

# Arquivo de flag para verificar se já foi executado
FIRST_BOOT_FLAG="/var/lib/first_boot_completed"

# Verificar se já foi executado
if [[ -f "$FIRST_BOOT_FLAG" ]]; then
    show_header
    log "Script de primeiro boot já foi executado anteriormente."
    exit 0
fi

# Mostrar header inicial
show_header

# Configurações padrão (plug and play)
ZABBIX_HOSTNAME="MUDAR"
TACTICAL_CLIENT_ID="1"
TACTICAL_CLIENT_FILIAL="1"

show_progress 1 10 "Iniciando configuração automática..."
sleep 2

show_status "CONFIGURAÇÕES APLICADAS" "Hostname: $ZABBIX_HOSTNAME | Client ID: $TACTICAL_CLIENT_ID | Site ID: $TACTICAL_CLIENT_FILIAL"
sleep 2

# Aguardar conectividade de rede
show_progress 2 10 "Verificando conectividade de rede..."
show_status "AGUARDANDO REDE" "Testando conexão com internet..."

for i in {1..30}; do
    if ping -c 1 ******* > /dev/null 2>&1; then
        log "Conectividade de rede estabelecida"
        break
    fi
    if [[ $i -eq 30 ]]; then
        error "Falha ao estabelecer conectividade de rede após 30 tentativas"
    fi
    show_status "AGUARDANDO REDE" "Tentativa $i/30 - Aguardando conexão..."
    sleep 2
done

# Aguardar um pouco mais para estabilizar a rede
show_progress 3 10 "Estabilizando conexão de rede..."
sleep 5

# Baixar o script principal do GitHub
show_progress 4 10 "Baixando script de instalação do GitHub..."
show_status "DOWNLOAD" "Conectando ao repositório GitHub..."

cd /tmp

# Token e URL do GitHub
GITHUB_TOKEN="****************************************"
GITHUB_URL="https://raw.githubusercontent.com/nubium-cloud/Zabbix-Proxy-Instalation/refs/heads/main/install_zabbix_tactical_rmm.sh"

# Baixar com autenticação
if wget --header="Authorization: token $GITHUB_TOKEN" "$GITHUB_URL" -O install_zabbix_tactical_rmm.sh; then
    log "Script baixado com sucesso"
    chmod +x install_zabbix_tactical_rmm.sh
else
    error "Falha ao baixar o script do GitHub"
fi

# Preparar script para execução automática
show_progress 5 10 "Preparando instalação automática..."
show_status "CONFIGURAÇÃO" "Modificando script para execução não-interativa..."

# Criar versão modificada do script que não requer interação
cp install_zabbix_tactical_rmm.sh install_zabbix_tactical_rmm_auto.sh

# Modificar o script para usar valores padrão sem prompts
sed -i "s/read -p \"Digite o nome do Zabbix Proxy.*$/ZABBIX_HOSTNAME=\"$ZABBIX_HOSTNAME\"/" install_zabbix_tactical_rmm_auto.sh
sed -i "s/read -p \"Digite o ID do Cliente.*$/TACTICAL_CLIENT_ID=\"$TACTICAL_CLIENT_ID\"/" install_zabbix_tactical_rmm_auto.sh
sed -i "s/read -p \"Digite a Filial do Cliente.*$/TACTICAL_CLIENT_FILIAL=\"$TACTICAL_CLIENT_FILIAL\"/" install_zabbix_tactical_rmm_auto.sh

# Remover verificações de entrada vazia
sed -i '/if \[\[ -z "\$ZABBIX_HOSTNAME" \]\]; then/,+2d' install_zabbix_tactical_rmm_auto.sh
sed -i '/if \[\[ -z "\$TACTICAL_CLIENT_ID" \]\]; then/,+2d' install_zabbix_tactical_rmm_auto.sh
sed -i '/if \[\[ -z "\$TACTICAL_CLIENT_FILIAL" \]\]; then/,+2d' install_zabbix_tactical_rmm_auto.sh

# Configurar para aceitar automaticamente o IP proposto
sed -i 's/read -p "Confirma a configuração do IP fixo.*$/CONFIRM_IP="s"/' install_zabbix_tactical_rmm_auto.sh
sed -i 's/read -p "Deseja usar um IP fixo diferente.*$/USE_DIFFERENT_IP="N"/' install_zabbix_tactical_rmm_auto.sh
sed -i 's/read -p "⚠️  Deseja continuar com a configuração de IP fixo.*$/CONFIRM_SSH_RISK="s"/' install_zabbix_tactical_rmm_auto.sh

chmod +x install_zabbix_tactical_rmm_auto.sh

# Executar instalação
show_progress 6 10 "Executando instalação do Zabbix e Tactical RMM..."
show_status "INSTALANDO" "Esta etapa pode levar 10-15 minutos. Aguarde..."

# Executar o script modificado em background e capturar saída
if ./install_zabbix_tactical_rmm_auto.sh > /tmp/install_output.log 2>&1; then
    log "Instalação concluída com sucesso!"
else
    warning "Instalação concluída com avisos. Verificando serviços..."
fi

# Verificar serviços
show_progress 7 10 "Verificando serviços instalados..."
show_status "VERIFICAÇÃO" "Testando Zabbix Proxy e Agent..."

# Verificar Zabbix Proxy
if systemctl is-active --quiet zabbix-proxy; then
    log "Zabbix Proxy está ativo"
else
    warning "Zabbix Proxy não está ativo. Tentando iniciar..."
    systemctl start zabbix-proxy || warning "Falha ao iniciar Zabbix Proxy"
fi

# Verificar Zabbix Agent
if systemctl is-active --quiet zabbix-agent; then
    log "Zabbix Agent está ativo"
else
    warning "Zabbix Agent não está ativo. Tentando iniciar..."
    systemctl start zabbix-agent || warning "Falha ao iniciar Zabbix Agent"
fi

# Criar arquivo de informações
show_progress 8 10 "Criando arquivo de informações do sistema..."
show_status "FINALIZANDO" "Gerando documentação e configurações finais..."

# Determinar diretório do usuário
USER_HOME="/home/<USER>"
if [[ ! -d "/home/<USER>" ]]; then
    # Se /home/<USER>
    if [[ -d "/home/<USER>" ]]; then
        USER_HOME="/home/<USER>"
    elif [[ -d "/home/<USER>" ]]; then
        USER_HOME="/home/<USER>"
    elif [[ -d "/home/<USER>" ]]; then
        USER_HOME="/home/<USER>"
    else
        # Criar diretório suportenv se não existir
        mkdir -p /home/<USER>
        USER_HOME="/home/<USER>"
        # Criar usuário suportenv se não existir
        if ! id "suportenv" &>/dev/null; then
            useradd -m -s /bin/bash suportenv 2>/dev/null || true
            # Adicionar ao grupo sudo se existir
            if getent group sudo > /dev/null 2>&1; then
                usermod -aG sudo suportenv 2>/dev/null || true
            fi
        fi
    fi
fi

# Garantir que o diretório existe
mkdir -p "$USER_HOME"

cat > "$USER_HOME/SISTEMA_INFO.txt" << EOF
=== RASPBERRY PI CONFIGURADO AUTOMATICAMENTE ===
Data da configuração: $(date)
Desenvolvido por: Paulo Matheus - NVirtual

=== CONFIGURAÇÕES APLICADAS ===
Zabbix Hostname: $ZABBIX_HOSTNAME
Tactical Client ID: $TACTICAL_CLIENT_ID
Tactical Site ID: $TACTICAL_CLIENT_FILIAL

=== PRÓXIMOS PASSOS ===
1. Acesse o Zabbix Server (monitora.nvirtual.com.br)
2. Altere o nome do proxy de "$ZABBIX_HOSTNAME" para o nome desejado
3. O sistema já está monitorado pelo Tactical RMM

=== INFORMAÇÕES TÉCNICAS ===
IP do sistema: $(hostname -I | awk '{print $1}')
Hostname: $(hostname)
Zabbix Server: monitora.nvirtual.com.br
Tactical Mesh: mesh.centralmesh.nvirtual.com.br
Tactical API: api.centralmesh.nvirtual.com.br

=== COMANDOS ÚTEIS ===
Verificar status Zabbix Proxy: sudo systemctl status zabbix-proxy
Verificar status Zabbix Agent: sudo systemctl status zabbix-agent
Logs Zabbix Proxy: sudo tail -f /var/log/zabbix/zabbix_proxy.log
Logs Zabbix Agent: sudo tail -f /var/log/zabbix/zabbix_agentd.log

=== ALTERAÇÃO DO HOSTNAME ZABBIX ===
Para alterar o hostname do Zabbix Proxy:
1. sudo nano /etc/zabbix/zabbix_proxy.conf
2. Alterar linha: Hostname=NOVO_NOME
3. sudo systemctl restart zabbix-proxy
EOF

# Definir proprietário correto do arquivo
USER_NAME=$(basename "$USER_HOME")
if id "$USER_NAME" &>/dev/null; then
    chown "$USER_NAME:$USER_NAME" "$USER_HOME/SISTEMA_INFO.txt"
else
    # Se usuário não existe, manter como root mas dar permissões de leitura
    chmod 644 "$USER_HOME/SISTEMA_INFO.txt"
fi

# Finalizar instalação
show_progress 9 10 "Marcando instalação como concluída..."

# Marcar como concluído
touch "$FIRST_BOOT_FLAG"
echo "$(date): Instalação automática concluída com sucesso" > "$FIRST_BOOT_FLAG"
chmod 644 "$FIRST_BOOT_FLAG"

# Limpar arquivos temporários
rm -f /tmp/install_zabbix_tactical_rmm.sh
rm -f /tmp/install_zabbix_tactical_rmm_auto.sh

show_progress 10 10 "Instalação concluída com sucesso!"

# Mostrar tela final de sucesso
show_final_screen() {
    clear > /dev/tty1 2>/dev/null || true
    local final_screen="
╔══════════════════════════════════════════════════════════════╗
║                    ✅ INSTALAÇÃO CONCLUÍDA!                 ║
╠══════════════════════════════════════════════════════════════╣
║                                                              ║
║  🎉 Raspberry Pi configurado com sucesso!                   ║
║                                                              ║
║  📋 CONFIGURAÇÕES APLICADAS:                                ║
║  • Zabbix Hostname: $ZABBIX_HOSTNAME                                      ║
║  • Tactical Client ID: $TACTICAL_CLIENT_ID                                        ║
║  • Tactical Site ID: $TACTICAL_CLIENT_FILIAL                                        ║
║                                                              ║
║  🔧 PRÓXIMOS PASSOS:                                        ║
║  1. Acesse: monitora.nvirtual.com.br                       ║
║  2. Altere nome do proxy de 'MUDAR' para nome desejado     ║
║  3. Sistema já está no Tactical RMM                        ║
║                                                              ║
║  📄 Informações salvas em: $USER_HOME/SISTEMA_INFO.txt       ║
║                                                              ║
║  🌐 IP do sistema: $(hostname -I | awk '{print $1}' | head -c 15)                        ║
║                                                              ║
║  Desenvolvido por: Paulo Matheus - NVirtual                ║
╚══════════════════════════════════════════════════════════════╝

Pressione ENTER para continuar ou aguarde 30 segundos para reiniciar...
"
    echo -e "${GREEN}$final_screen${NC}" > /dev/tty1 2>/dev/null || true
    echo -e "${GREEN}$final_screen${NC}"
}

show_final_screen

# Aguardar input do usuário ou timeout
read -t 30 -p "" || true

log "🎉 Sistema configurado com sucesso!"
log "📄 Informações salvas em: $USER_HOME/SISTEMA_INFO.txt"
log "🔧 Para alterar o hostname Zabbix, edite: /etc/zabbix/zabbix_proxy.conf"
log "🌐 Acesse o Zabbix em: monitora.nvirtual.com.br"

# Opcional: Reiniciar para aplicar todas as configurações
show_status "FINALIZAÇÃO" "Preparando para reiniciar o sistema..."
log "Reiniciando sistema em 10 segundos para finalizar configuração..."
sleep 10
reboot
