#!/bin/bash

# Script para preparar imagem bootstrap do Rasp<PERSON> Pi
# Esta imagem baixa automaticamente scripts atualizados do GitHub
# Autor: <PERSON> - NVirtual
# Data: $(date +%Y-%m-%d)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERRO] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[AVISO] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

log "=== PREPARADOR DE IMAGEM BOOTSTRAP RASPBERRY PI ==="
log "Criando imagem que baixa scripts atualizados do GitHub"
log "Desenvolvido por Paulo <PERSON> - NVirtual"

# Verificar se está rodando como root
if [[ $EUID -ne 0 ]]; then
   error "Este script deve ser executado como root (sudo)"
fi

# Verificar arquivos necessários
log "Verificando arquivos necessários..."
REQUIRED_FILES=("bootstrap_first_boot.sh" "install_bootstrap_service.sh")
for file in "${REQUIRED_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        error "Arquivo necessário não encontrado: $file"
    fi
done

log "✅ Todos os arquivos necessários encontrados"

# Atualizar sistema base
log "Atualizando sistema base..."
apt-get update -y
apt-get upgrade -y

# Instalar dependências básicas
log "Instalando dependências básicas..."
apt-get install -y curl wget git vim nano htop

# Configurar timezone para Brasil
log "Configurando timezone..."
timedatectl set-timezone America/Sao_Paulo

# Habilitar SSH se não estiver habilitado
log "Verificando SSH..."
if ! systemctl is-enabled ssh > /dev/null 2>&1; then
    log "Habilitando SSH..."
    systemctl enable ssh
    systemctl start ssh
else
    log "✅ SSH já está habilitado"
fi

# Executar o instalador do serviço bootstrap
log "Instalando serviço bootstrap..."
chmod +x install_bootstrap_service.sh
./install_bootstrap_service.sh

# Configurar hostname padrão
log "Configurando hostname padrão..."
echo "raspberrypi-bootstrap" > /etc/hostname
sed -i 's/*********.*/*********\traspberrypi-bootstrap/' /etc/hosts

# Configurar mensagem de boot
log "Configurando mensagem de boot..."
cat > /etc/issue << 'EOF'

 ____              _       _                   
| __ )  ___   ___ | |_ ___| |_ _ __ __ _ _ __   
|  _ \ / _ \ / _ \| __/ __| __| '__/ _` | '_ \  
| |_) | (_) | (_) | |_\__ \ |_| | | (_| | |_) | 
|____/ \___/ \___/ \__|___/\__|_|  \__,_| .__/  
                                       |_|     

=== RASPBERRY PI BOOTSTRAP ===
🔄 Scripts sempre atualizados do GitHub
📥 Download automático no primeiro boot
🚀 Configuração sempre na versão mais recente

Desenvolvido por Paulo Matheus - NVirtual

EOF

# Configurar mensagem de boas-vindas
log "Configurando mensagem de boas-vindas..."
cat > /etc/motd << 'EOF'

 ____              _       _                   
| __ )  ___   ___ | |_ ___| |_ _ __ __ _ _ __   
|  _ \ / _ \ / _ \| __/ __| __| '__/ _` | '_ \  
| |_) | (_) | (_) | |_\__ \ |_| | | (_| | |_) | 
|____/ \___/ \___/ \__|___/\__|_|  \__,_| .__/  
                                       |_|     

=== RASPBERRY PI BOOTSTRAP ===
Desenvolvido por Paulo Matheus - NVirtual

🔄 BOOTSTRAP AUTOMÁTICO NO PRIMEIRO BOOT
   - Scripts baixados automaticamente do GitHub
   - Sempre na versão mais recente
   - Não precisa recriar imagem para updates

📋 VERIFICAR STATUS: check-bootstrap
📄 INFORMAÇÕES: cat /home/<USER>/IMAGEM_BOOTSTRAP_INFO.txt

⚠️  IMPORTANTE: 
   • Certifique-se de ter conexão com internet no primeiro boot
   • O sistema baixará e executará scripts do GitHub automaticamente

EOF

# Configurar usuário suportenv se não existir
log "Configurando usuário suportenv..."
if ! id "suportenv" &>/dev/null; then
    log "Criando usuário suportenv..."
    useradd -m -s /bin/bash suportenv
    
    # Adicionar ao grupo sudo
    if getent group sudo > /dev/null 2>&1; then
        usermod -aG sudo suportenv
    fi
    
    # Configurar bashrc básico
    cat >> /home/<USER>/.bashrc << 'EOF'

# Configurações NVirtual
export PS1='\[\033[01;32m\]\u@\h\[\033[00m\]:\[\033[01;34m\]\w\[\033[00m\]\$ '
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias check-bootstrap='sudo /usr/local/bin/check_bootstrap_status.sh'

echo "Bem-vindo ao Raspberry Pi Bootstrap NVirtual!"
echo "Para verificar status: check-bootstrap"
EOF

    chown suportenv:suportenv /home/<USER>/.bashrc
    log "✅ Usuário suportenv configurado"
else
    log "✅ Usuário suportenv já existe"
fi

# Criar arquivo de informações detalhado
log "Criando arquivo de informações detalhado..."
cat > /root/BOOTSTRAP_SETUP_INFO.txt << EOF
=== CONFIGURAÇÃO BOOTSTRAP RASPBERRY PI ===

Data de configuração: $(date)
Sistema: $(uname -a)
Versão OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
Desenvolvido por: Paulo Matheus - NVirtual

=== SERVIÇOS CONFIGURADOS ===
• raspberry-bootstrap.service (habilitado)
• SSH (habilitado)
• Timezone: America/Sao_Paulo

=== USUÁRIOS CONFIGURADOS ===
$(for user in suportenv pi ubuntu debian; do
    if id "$user" &>/dev/null 2>&1; then
        echo "• $user (UID: $(id -u $user), Grupos: $(groups $user | cut -d: -f2))"
    fi
done)

=== SCRIPTS INSTALADOS ===
• /usr/local/bin/bootstrap_first_boot.sh
• /usr/local/bin/check_bootstrap_status.sh

=== REPOSITÓRIO GITHUB ===
• Repositório: nubium-cloud/Raspberry-First-Boot-Configuration
• Token configurado: Sim
• Script principal: setup_first_boot.sh

=== FLUXO DE EXECUÇÃO ===
1. Boot do sistema
2. Serviço raspberry-bootstrap.service inicia
3. Script bootstrap_first_boot.sh executa
4. Download do script mais atual do GitHub
5. Execução da configuração automática
6. Instalação Zabbix + Tactical RMM
7. Reinicialização do sistema

=== VANTAGENS ===
✅ Scripts sempre atualizados
✅ Não precisa recriar imagem
✅ Manutenção centralizada no GitHub
✅ Configuração sempre na versão mais recente

=== COMANDOS DE VERIFICAÇÃO ===
check-bootstrap                           # Status geral
sudo journalctl -u raspberry-bootstrap.service -f  # Logs em tempo real
sudo systemctl status raspberry-bootstrap.service  # Status do serviço

=== DESENVOLVIDO POR ===
Paulo Matheus - NVirtual
EOF

# Limpar histórico e logs para imagem limpa
log "Limpando sistema para imagem..."
history -c
> /home/<USER>/.bash_history 2>/dev/null || true
> /home/<USER>/.bash_history 2>/dev/null || true
journalctl --vacuum-time=1d
apt-get autoremove -y
apt-get autoclean

# Remover arquivos de instalação
log "Removendo arquivos de instalação..."
rm -f install_bootstrap_service.sh

log "✅ Preparação da imagem bootstrap concluída!"
echo
echo "=========================================="
echo "IMAGEM BOOTSTRAP RASPBERRY PI PRONTA"
echo "=========================================="
echo
info "🎯 A imagem bootstrap está pronta para ser clonada!"
echo
info "📋 Características da imagem:"
info "• Sistema base atualizado e otimizado"
info "• SSH habilitado"
info "• Usuário suportenv configurado"
info "• Serviço bootstrap instalado"
info "• Scripts baixados automaticamente do GitHub"
echo
info "🔄 VANTAGENS DO BOOTSTRAP:"
info "• ✅ Scripts sempre na versão mais atual"
info "• ✅ Não precisa recriar imagem para updates"
info "• ✅ Manutenção centralizada no GitHub"
info "• ✅ Token de acesso configurado"
echo
warning "⚠️  PRÓXIMOS PASSOS:"
warning "1. Desligue o Raspberry Pi: sudo shutdown -h now"
warning "2. Clone o SD card para criar imagem bootstrap"
warning "3. Esta será sua imagem DEFINITIVA - sempre atualizada!"
echo
info "🔧 COMO USAR A IMAGEM BOOTSTRAP:"
info "1. Grave a imagem em novos SD cards"
info "2. Insira no Raspberry Pi e conecte à internet"
info "3. Ligue o sistema"
info "4. O bootstrap baixará e executará scripts atualizados"
info "5. Aguarde instalação automática (10-15 minutos)"
info "6. Sistema estará configurado com versão mais recente"
echo
info "📝 ATUALIZAÇÕES FUTURAS:"
info "• Edite scripts no GitHub"
info "• Novos deployments usarão versão atualizada automaticamente"
info "• Não precisa recriar imagem nunca mais!"
echo
log "🎉 Imagem bootstrap definitiva criada com sucesso!"
echo
info "📄 Informações detalhadas salvas em: /root/BOOTSTRAP_SETUP_INFO.txt"
